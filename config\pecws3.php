<?php
// config/pecws.php

return [
    'user_id' => env('PECWS_DEV_USERID'),
    'cert_id' => env('PECWS_DEV_CERTID'),
    'username' => env('PECWS_DEV_USERID') . ":" . env('PECWS_DEV_CERTID'),
    'password' => env('PECWS_DEV_PASSWORD'),
    'hospitalcode' => env('PECWS_DEV_HOSPITALCODE'),
    'pmcc' => env('PECWS_DEV_PMCC'),
    'cypherkey' => env('PECWS_DEV_CYPHERKEY'),
    'eclaims_cipher_key' => env('ECLAIMS_CIPHER_KEY'),
    'ekonsulta_cipher_key' => env('EKONSULTA_CYPHERKEY'),

    'eclaims_dev_url' => env('PECWS_DEV_BASE_URL'),
    'eclaims_dev_accreditation_no' => env('PECWS_DEV_ACCREDITATION_NUMBER'),
    'eclaims_dev_software_certificate_id' => env('PECWS_DEV_CERTIFICATION_ID'),
    'eclaims_dev_accreno' => env('PECWS_DEV_ACCREDITATION_NUMBER'),
];
