<?xml version="1.0" encoding="UTF-8"?>
<!--
  <PERSON><PERSON><PERSON> define vocabulary identification
  PUBLIC ID: -//vendor//vocabulary//EN
  SYSTEM ID: http://server/path/CF5.dtd
  
-->
<!--
  An example how to use this DTD from your XML document:
  
  <?xml version="1.0"?>
  
  <!DOCTYPE CF5 SYSTEM "CF5.dtd">
  
  <CF5>
  ...
  </CF5>
-->
<!--
  -
  Put your DTDDoc comment here.
-->
<xs:schema xmlns:xs="http://www.w3.org/2001/XMLSchema" elementFormDefault="qualified">
  <xs:element name="CF5">
    <xs:complexType>
      <xs:complexContent>
        <xs:extension base="DRGCLAIM">
          <xs:attribute name="pHospitalCode" use="required"/>
        </xs:extension>
      </xs:complexContent>
    </xs:complexType>
  </xs:element>
  <!--
    -
    Put your DTDDoc comment here.
  -->
  <xs:complexType name="DRGCLAIM">
    <xs:sequence>
      <xs:element ref="DRGCLAIM"/>
    </xs:sequence>
  </xs:complexType>
  <xs:element name="DRGCLAIM">
    <xs:complexType>
      <xs:sequence>
        <xs:element ref="SECONDARYDIAGS"/>
        <xs:element ref="PROCEDURES"/>
      </xs:sequence>
      <xs:attribute name="ClaimNumber" use="required"/>
      <xs:attribute name="PrimaryCode" use="required"/>
      <xs:attribute name="NewBornAdmWeight" use="required"/>
      <xs:attribute name="Remarks" use="required"/>
    </xs:complexType>
  </xs:element>
  <!--
    -
    Put your DTDDoc comment here.
  -->
  <xs:element name="SECONDARYDIAGS">
    <xs:complexType>
      <xs:sequence>
        <xs:element minOccurs="0" maxOccurs="unbounded" ref="SECONDARYDIAG"/>
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <!--
    -
    Put your DTDDoc comment here.
  -->
  <xs:element name="SECONDARYDIAG">
    <xs:complexType>
      <xs:attribute name="SecondaryCode" use="required"/>
      <xs:attribute name="Remarks" use="required"/>
    </xs:complexType>
  </xs:element>
  <!--
    -
    Put your DTDDoc comment here.
  -->
  <xs:element name="PROCEDURES">
    <xs:complexType>
      <xs:sequence>
        <xs:element minOccurs="0" maxOccurs="unbounded" ref="PROCEDURE"/>
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <!--
    -
    Put your DTDDoc comment here.
  -->
  <xs:element name="PROCEDURE">
    <xs:complexType>
      <xs:attribute name="RvsCode" use="required"/>
      <xs:attribute name="Laterality" use="required"/>
      <xs:attribute name="Ext1" use="required"/>
      <xs:attribute name="Ext2" use="required"/>
      <xs:attribute name="Remarks" use="required"/>
    </xs:complexType>
  </xs:element>
</xs:schema>
