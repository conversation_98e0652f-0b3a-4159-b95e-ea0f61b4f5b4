<?php

namespace App\Services\XMLServices;

use App\Repositories\EClaimsRepository;
use App\Services\ExternalAPIService;
use App\Services\IO\IOService;
use App\Services\PhilhealthEncryptionService;
use App\Services\Validators\eClaimsValidatorService;
use Exception;
use Illuminate\Database\Eloquent\Casts\Json;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;
use Symfony\Component\HttpFoundation\BinaryFileResponse;

class eClaimsXMLService
{
    // use HasPECWSToken;
    protected $eClaimsRepository;
    protected $eClaimsXMLvalidator;

    protected $encryptionService;
    protected $externalAPIService;
    protected $ioService;

    private $accreno;
    /**
     * Create a new class instance.
     */
    public function __construct(
        EClaimsRepository $eClaimsRepository,
        eClaimsValidatorService $eClaimsXMLvalidator,
        PhilhealthEncryptionService $encryptionService,
        ExternalAPIService $externalAPIService,
        IOService $ioService
    ) {
        $this->eClaimsRepository = $eClaimsRepository;
        $this->eClaimsXMLvalidator = $eClaimsXMLvalidator;
        $this->encryptionService = $encryptionService;
        $this->externalAPIService = $externalAPIService;
        $this->accreno = config("pecws3.eclaims_dev_accreditation_no");
        $this->ioService = $ioService;
    }

    public function generate($enccode)
    {
        return $this->eClaimsRepository->generateeClaimsData($enccode);
    }
    public function retrieve($enccode)
    {
        return $this->eClaimsRepository->retrieveeClaimsData($enccode);
    }

    public function validate_local($xml)
    {
        return $this->eClaimsXMLvalidator->validateXML($xml);
    }

    public function encrypt($xml)
    {
        return $this->encryptionService->encryptMessage($xml);
    }

    public function decrypt($xml)
    {
        return $this->encryptionService->decryptMessage($xml);
    }

    public function validate_remote($payload)
    {

        return $this->externalAPIService->post('eClaimsFileCheck', $payload);

    }

    public function writeToDisk($pdfPath, $pdfData)
    {
        return $this->ioService->writePDFToDisk($pdfPath, $pdfData);
    }

    public function eClaimsFileCheck(string $enccode)
    {
        try {

            // 1. Generate XML from repository
            $data = $this->generate($enccode);

            if ($data['success'] == false) {
                return $data;
            }
            // return $data;
            // $pHospitalTransmittalNo = $data['data'][0]->pHospitalTransmittalNo;

            //check if $data is array
            // if (!is_array($data)) {
            //     return ['success' => false, 'message' => 'Invalid data format'];
            // }

            $xml = $data['data'][0]->claimXML;
            //2. Validate claim XML against local DTD
            $response = $this->validate_local($xml);

            // return $response;

            if ($response['status'] == false) {
                //create new Exception to be thrown
                // $e = new Exception("XML Validation Failed: " . $response['errors']);
                return $response;
            }

            // 3. Encrypt the XML
            $encryptedXml = $this->encrypt($xml);

            // 4. Post encrypted XML to external API (retry logic)
            $response = $this->retry(3, function () use ($encryptedXml) {
                return $this->validate_remote($encryptedXml);
            });
            // $response = $this->validate_remote($encryptedXml);

            // Log success

            //if response has "success": true then return true
            $return = [
                "success" => $response['success'],
                "data" => $response->getBody()
            ];

            Log::info("Successfully processed eClaims for enccode: $enccode", ['response' => $return]);

            return $this->eClaimsRepository->saveeClaimsData($data);

        } catch (Exception $e) {
            // Log errors
            Log::error("eClaims processing failed for enccode: $enccode", ['error' => $e->getMessage()]);
            throw $e;
        }
    }

    private function retry($attempts, callable $callback)
    {
        $tries = 0;
        while ($tries < $attempts) {
            try {
                return $callback();
            } catch (Exception $e) {
                $tries++;
                if ($tries >= $attempts) {
                    throw new Exception("API Request Failed after $attempts attempts: " . $e->getMessage());
                }
                sleep(1); // Short delay before retrying
            }
        }
    }

    public function uploadeClaims(string $enccode)
    {


        //1. Retrieve eClaims data
        $data = $this->retrieve($enccode);

        // return $data;

        //2. Encrypt the XML
        $encryptedXml = $this->encrypt($data['data'][0]->claimXML);

        //3. Post encrypted XML to external API
        $response = $this->externalAPIService->post('uploadeClaims', $encryptedXml);

        //4. Read response and return
        $encryptedResult = $response['result'];

        //5. Decrypt the result
        $decryptedResult = $this->decrypt($encryptedResult) . '>';

        // return simplexml_load_string($decryptedResult) ? true : false;

        //6. Check if result is XML format
        if (!$this->eClaimsXMLvalidator->isValidXML($decryptedResult)) {
            return ['success' => false, 'message' => 'Invalid XML format'];
        }

        $parsedXML = $this->eClaimsXMLvalidator->parseeClaimsXML($decryptedResult);

        return $this->eClaimsRepository->saveeCLaimsMapData($parsedXML['@attributes']);
    }

    public function isClaimEligible(string $enccode): JsonResponse
    {
        try {
            // 1. Get payload from database
            $data = $this->eClaimsRepository->getIsClaimEligiblePayload($enccode);
            // return response()->json($data);
            if (!$data['success']) {
                return response()->json($data, 404);
            }

            // return response()->json($data['data']);
            // 2. Encrypt data
            $encryptedData = $this->encrypt(json_encode($data['data']));
            // $encryptedData = $this->encrypt(json_encode($data));

            // return response()->json($encryptedData);
            // 3. Post encrypted data to external API
            $apiResponse = $this->externalAPIService->post('isClaimEligible', $encryptedData);

            // return response()->json($apiResponse['result']);
            // 4. Decrypt result
            $decryptedResult = $this->decrypt($apiResponse['result']);
            // return $decryptedResult;

            $decryptedResult = $decryptedResult . '}';
            $decryptedResult = json_decode($decryptedResult, true);

            if (!$decryptedResult || !isset($decryptedResult['referenceno'])) {
                return response()->json(['error' => 'Decryption failed or invalid data'], 500);
            }

            // 5. Generate payload for PDF request
            $referenceNo = $decryptedResult['referenceno'];
            $pdfPayload = ['accreno' => $this->accreno, 'referenceno' => $referenceNo];

            // 6. Request PDF generation
            $pdfResponse = $this->externalAPIService->post('generatePBEFPDF', $pdfPayload);
            if (!isset($pdfResponse['result'])) {
                return response()->json(['error' => 'Failed to generate PDF'], 500);
            }

            // 7. Decode PDF content
            $decryptedPdfData = $this->decrypt($pdfResponse['result']);
            $pdf = base64_decode($decryptedPdfData);
            if (!$pdf) {
                return response()->json(['error' => 'Invalid PDF data'], 500);
            }

            // 8. Store PDF
            $pdfPath = "pbef/{$referenceNo}.pdf";

            $io = $this->ioService->writePDFToDisk($pdfPath, $pdf);

            if (!$io['success']) {
                return response()->json(['error' => 'Failed to write PDF to storage'], 500);
            }

            //9. Return success response
            return response()->json(['success' => true, 'referenceno' => $referenceNo, 'pdfPath' => $pdfPath]);

        } catch (Exception $e) {
            return response()->json(['error' => 'An error occurred', 'details' => $e->getMessage()], 500);
        }
    }


    public function eCLaimsPBEFPDF(string $pdfPath): BinaryFileResponse|array
    {
        // return [$pdfPath];
        //check if file exists
        // return response()->json($pdfPath);
        if (!Storage::disk('eclaims')->exists($pdfPath)) {
            return ['success' => false, 'error' => "File '{$pdfPath}' not found!", 'path' => $pdfPath];
        }
        // return response()->file(storage_path($pdfPath));
        return response()->file(Storage::disk('eclaims')->path($pdfPath));
    }


    public function getClaimStatus(array $claimsSeriesNumbers)
    {
        $response = $this->externalAPIService->post('getClaimStatus', $claimsSeriesNumbers);

        return $response;
    }
}
