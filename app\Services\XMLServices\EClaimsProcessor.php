<?php

namespace App\Services\XMLServices;

use App\Interfaces\Contracts\XmlProcessorInterface;
use App\Repositories\EClaimsRepository;
use App\Services\Validators\eClaimsValidatorService;

class EClaimsProcessor implements XmlProcessorInterface
{


    private EClaimsRepository $eClaimsRepository;
    private eClaimsValidatorService $validatorService;
    public function __construct(
        EClaimsRepository $eClaimsRepository,
        EClaimsValidatorService $eClaimsXMLvalidator
    ) {
        //
        $this->eClaimsRepository = $eClaimsRepository;
        $this->validatorService = $eClaimsXMLvalidator;
    }
    public function retrieve($enccode): string
    {
        return $this->eClaimsRepository->geteClaimsData($enccode);
    }

    public function validate_local(string $xml): array
    {
        return $this->validatorService->validateXML($xml);
    }

    public function encrypt(string $xml): string
    {
        return base64_encode($xml);
    }

    public function validate_remote(string $encryptedPayload): bool
    {
        return !empty($encryptedPayload);
    }

    public function post(string $encryptedPayload): array
    {
        return ['status' => 'success'];
    }
}