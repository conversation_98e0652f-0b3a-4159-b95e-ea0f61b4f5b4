<?php

namespace App\Services;

use DOMDocument;

class ESOAGenerationService
{
    protected $dom;

    public function __construct()
    {
        // Initialize DOMDocument for XML generation
        $this->dom = new DOMDocument('1.0', 'UTF-8');
        $this->dom->formatOutput = true;
    }

    /**
     * Generate XML based on DTD structure
     *
     * @param array $data - Data input to populate XML
     * @return string - Generated XML as a string
     */
    public function generateXml(array $data): string
    {
        // Create root element <eSOA>
        $eSOA = $this->dom->createElement('eSOA');
        $this->dom->appendChild($eSOA);

        // Generate <SummaryOfFees>
        $summaryOfFees = $this->createSummaryOfFees($data['SummaryOfFees'] ?? []);
        $eSOA->appendChild($summaryOfFees);

        // Generate <ProfessionalFees>
        $professionalFees = $this->createProfessionalFees($data['ProfessionalFees'] ?? []);
        $eSOA->appendChild($professionalFees);

        // Generate <ItemizedBillingItems>
        $itemizedBillingItems = $this->createItemizedBillingItems($data['ItemizedBillingItems'] ?? []);
        $eSOA->appendChild($itemizedBillingItems);

        // Return XML as a string
        return $this->dom->saveXML();
    }

    protected function createSummaryOfFees(array $data): \DOMElement
    {
        $element = $this->dom->createElement('SummaryOfFees');

        // Add child elements according to the DTD structure
        $fields = ['RoomAndBoard', 'DrugsAndMedicine', 'LaboratoryAndDiagnostic', 'OperatingRoomFees', 'MedicalSupplies', 'PhilHealth', 'Balance'];
        foreach ($fields as $field) {
            $child = $this->dom->createElement($field, $data[$field] ?? '');
            $element->appendChild($child);
        }

        return $element;
    }

    protected function createProfessionalFees(array $data): \DOMElement
    {
        $element = $this->dom->createElement('ProfessionalFees');

        // Handle ProfessionalFee elements (can be multiple)
        if (isset($data['ProfessionalFee']) && is_array($data['ProfessionalFee'])) {
            foreach ($data['ProfessionalFee'] as $fee) {
                $child = $this->dom->createElement('ProfessionalFee', $fee);
                $element->appendChild($child);
            }
        }

        // Add PhilHealth and Balance
        $element->appendChild($this->dom->createElement('PhilHealth', $data['PhilHealth'] ?? ''));
        $element->appendChild($this->dom->createElement('Balance', $data['Balance'] ?? ''));

        return $element;
    }

    protected function createItemizedBillingItems(array $data): \DOMElement
    {
        $element = $this->dom->createElement('ItemizedBillingItems');

        // Example: Creating each billing item
        if (isset($data['BillingItem']) && is_array($data['BillingItem'])) {
            foreach ($data['BillingItem'] as $item) {
                $itemElement = $this->dom->createElement('BillingItem');
                $itemElement->appendChild($this->dom->createElement('Description', $item['Description'] ?? ''));
                $itemElement->appendChild($this->dom->createElement('Amount', $item['Amount'] ?? ''));
                $element->appendChild($itemElement);
            }
        }

        return $element;
    }
}
