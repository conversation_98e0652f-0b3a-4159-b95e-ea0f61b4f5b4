<?php

namespace App\Services;

use Illuminate\Support\Facades\Http;
use App\Services\EClaimsTokenService;

class ExternalAPIService
{

    protected $baseUrl;
    protected $token;

    protected $headers;

    protected $tokenService;
    /**
     * Create a new class instance.
     */
    public function __construct(EClaimsTokenService $tokenService)
    {
        //
        $this->tokenService = $tokenService;
        $this->baseUrl = config('pecws3.eclaims_dev_url');
        $this->setHeaders();

    }

    private function setHeaders()
    {
        $this->headers = [

            'token' => $this->tokenService->getPECWS3Token(),
            'Content-Type' => 'application/json',
        ];
    }

    public function post($url, $payload)
    {
        $url = $this->baseUrl . '/' . $url;
        // return $this->headers;
        // var_dump($this->headers);
        $response = Http::withHeaders($this->headers)->post($url, $payload);
        return $response;
    }
}
