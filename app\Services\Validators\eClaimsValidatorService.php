<?php

namespace App\Services\Validators;

class eClaimsValidatorService
{
    protected string $dtdPath;
    /**
     * Create a new class instance.
     */
    public function __construct()
    {
        //
        $this->dtdPath = 'file:///' . str_replace('\\', '/', realpath(app_path('Services/DTDs/Current/eClaimsDef.dtd')));
    }

    /**
     * Validate eClaims XML
     * @param string $xml
     * @return array ['status' => bool, 'errors' => array|null]
     */
    public function validateXML(string $xml): array
    {
        if (!$xml) {
            return ['status' => false, 'errors' => ['No eClaims XML data found']];
        }

        if (!file_exists($this->dtdPath)) {
            return ['status' => false, 'errors' => ["DTD file not found at {$this->dtdPath}"]];
        }

        $xml = "<?xml version=\"1.0\"?>\n<!DOCTYPE eCLAIMS SYSTEM \"$this->dtdPath\">\n$xml";
        $xml = preg_replace('/<\?xml[^>]+?\?>/', '', $xml);

        libxml_use_internal_errors(true);
        $doc = new \DOMDocument();
        $doc->preserveWhiteSpace = false;

        if (!$doc->loadXML($xml)) {
            $errors = libxml_get_errors();
            libxml_clear_errors();
            return ['status' => false, 'errors' => $this->formatLibXmlErrors($errors)];
        }

        if (!$doc->validate()) {
            $errors = libxml_get_errors();
            libxml_clear_errors();
            return ['status' => false, 'errors' => $this->formatLibXmlErrors($errors)];
        }

        return ['status' => true, 'errors' => null];
    }

    /**
     * Format libxml errors into a readable array
     * @param array $errors
     * @return array
     */
    private function formatLibXmlErrors(array $errors): array
    {
        return array_map(function ($error) {
            return trim($error->message) . " (Line {$error->line}, Column {$error->column})";
        }, $errors);
    }

    public function isValidXML($xml): bool
    {

        return simplexml_load_string($xml) ? true : false;
    }


    public function parseeClaimsXML($xml): array
    {
        $xml = simplexml_load_string($xml);
        $json = json_encode($xml);
        $array = json_decode($json, true);
        return $array;
    }


}
