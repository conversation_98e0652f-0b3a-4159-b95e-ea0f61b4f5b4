<?php

namespace App\Services\IO;

use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Storage;

class IOService
{
    /**
     * Create a new class instance.
     */
    public function __construct()
    {
        //
    }

    public function writePDFToDisk(string $pdfPath, string $pdfData): array
    {
        //check if storage folder eclaims
        if (!Storage::disk('eclaims')->exists('pbef')) {
            Storage::disk('eclaims')->makeDirectory('pbef');
        }

        $success = Storage::disk('eclaims')->put($pdfPath, $pdfData);
        if (!$success) {
            return ['succes' => false, 'error' => 'Failed to write PDF to storage'];
        }

        return ['success' => true, 'pdfPath' => $pdfPath];
    }

}
