<?php

namespace App\Http\Controllers;




use App\Services\EncryptionService;
use App\Services\PhilhealthEncryptionService;
use Illuminate\Http\Request;

class EncryptionController extends Controller
{
    //

    protected $philhealthEncryptionService;


    public function __construct(
        PhilhealthEncryptionService $philhealthEncryptionService
    ) {
        $this->philhealthEncryptionService = $philhealthEncryptionService;
    }

    public function encrypt(Request $request)
    {
        // return $request->getContent();
        // return $request->all();
        $data = $request->getContent();
        // return $data;
        // $data = json_encode($data);
        // return $data;
        // return json_encode($data);
        return $this->philhealthEncryptionService->encryptMessage($data);
        // return $this->philhealthEncryptionService->encryptMessage(json_encode($data));
    }

    public function decrypt(Request $request)
    {

        $data = $request->all();
        return $this->philhealthEncryptionService->decryptMessage($data);
    }

    public function encrypt_ekonsulta(Request $request)
    {
        $data = $request->getContent();
        return $this->philhealthEncryptionService->encryptMessage($data, isEclaims: 'ekonsulta');
    }

    public function decrypt_ekonsulta(Request $request)
    {
        $data = $request->all();
        return $this->philhealthEncryptionService->decryptMessage($data, 'ekonsulta');
    }
}
