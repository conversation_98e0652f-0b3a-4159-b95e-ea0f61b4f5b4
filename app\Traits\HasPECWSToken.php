<?php

namespace App\Traits;

use Illuminate\Support\Facades\Http;

trait HasPECWSToken
{
    //
    public function getPECWS3Token(): String
    {
        $headers = [
            'accreditationNo' => 'H13000204',
            'softwareCertificateId' => 'ECLAIMS-3.0-20101-DUMMY',
            'Content-Type' => 'application/json',
        ];
        $response = Http::withHeaders($headers)->get('http://ecstest.philhealth.gov.ph/PHIC/Claims3.0/getToken');

        return json_decode($response)->result;
    }
}
