<?xml version="1.0" encoding="UTF-8"?>
<!--
  Philippine Health Insurance Corporation
  EPCB Document Type Definition Version 1.20
  Version History
  0.1.0 01-15-2018
      : Initial draft
  
  0.1.1 01-16-2018
      : "Camelized" attribute names
  
  	0.1.2 04-24-2018 by Zia
  		: Added Laboratory Result
  		: Added Medicine in Consultation/SOAP MODULE
  
  	0.1.3 06-28-2018 by Zia
  		:Added Plan/Management in Profiling
  		:Added the Certification ID of XPS
  		:Modified Enlistment - added pWithDisability, pDependentType
  		:Modified Profiling - added pProfileOTP in PROFILE, removed pPrescType
  		:Modified Profiling - added elements of ADVICE, DIAGNOSTIC , MANAGEMENT, PEPERT, and PESPECIFIC
  		:Modified SOAP - added pOthRemarks in MANAGEMENT element, and delete OBLIGATED element
  		:Removed Meds attribute in SOAP
  		:Created MEDICINE element (added attributes pHciTransNo, pHciCaseNo, pModule)
  		:Modified LABRESULTS - added ECG, FECALYSIS, PAPSSMEAR, OGTT elements; added pModule attribute
  		:Removed pExtremeRem, pHeentPalploc, and pExtrmDeform in PESPECIFIC element
  		:Added pRectalRem and pGuRem in PESPECIFIC element
  		:Removed pExtremitiesId in PEMISC element
  		:Added pRectalId and pGuId in PEMISC element
  		:Added pDrugActualPrice, pDiagnosticLabFee, pReportStatus and pDeficiencyRemarks
  
  	0.1.4 07-09-2018 by Zia
  		:Changed pEnlistType into pPackageType in Enlistment element
  
  	0.1.5 07-10-2018 by Zia
  		:Added pAvailFreeService in Enlistment element
  		:Added needed value in pReportStatus
  
  	0.1.6 07-11-2018 by Zia
  		:Added Co-payment in Laboratory Results
  		:Added Co-payment in Medicine and changed parameter of Unit Price into pActualUnitPrice (as Drug Actual Price)
  	0.1.7 08-30-2018 by Zia
  		:Update Medicine element/attributes (library from DOH)	
  		:Update attributes based on CF4 
  	0.1.8 09-10-2018 by Zia
  		:Add Generic Survey, Course in the ward - date, doctors action
  	0.1.9 09-19-2018 by Zia
  		:Update pGenSurveyId (1|2) insert valid values (1 - Awake and Alert; 2 - Altered Sensorium)
  		:Update pPackageType (P|X) insert valid values (P - PCB1; X - EPCB; A - CF4)
  	0.1.10 09-27-2018 by Zia
  		:Removed Unit Code and Salt Code in MEDICINE
  		:Added pPrescPhysician and pIsApplicable in Medicine element
  		:Modified LABRESULT element
  		:Added pEClaimId (Claim ID) and pEClaimsTransmittalId for CF4 
  	0.1.11 09-28-2018 by Zia
  		: removed pWaist
  		: added pIsApplicable in LABRESULT
  	0.1.12 10-03-2018  by Zia
  		: added pDataCollection in SPUTUM 
  		: removed pFindings in FECALYSIS
  	0.1.13 10-5-2018 by Zia
  		: added pTransDate, pCreatedBy
  	0.1.14 10-09-2018 by Zia
  		: Added pGenericName in MEDICINE
  	0.1.15 12-20-2018 by Zia
  		:modify value accepted for pPackageType in ENLISTMENT from (P|X|A) to (P|E|A), E - for EPCB
  	0.1.16 01-29-2019 by Zia
  		:updated elements and attributes per requirements
  		:modified element operators
  	0.1.17 01-31-2019 by Zia
  		:added value of "X" as NOT APPLICABLE in SPUTUM
  		:read value of "X" as NOT APPLICABLE in element of ENLISTMENT
  	0.1.18 02-12-2019 by zia
  		:added pSignsSymptoms in SUBJECTIVE list for CF4 purposes
  	0.1.19 02-19-2019 by Zia
  		:added value of 'W' as waived in PAPSSMEAR and OGTT element
  	0.1.20 02-26-2019 by Zia
  		:added pSaltCode, pUnitCode, pRoute attributes in MEDICINE element (EPCB and CF4 purposes)
  		:added pIsApplicable attribute in MENSHIST
  		:added pPainSite attribute in SUBJECTIVE
-->
<!-- - Put your DTDDoc comment here. -->
<xs:schema xmlns:xs="http://www.w3.org/2001/XMLSchema" elementFormDefault="qualified">
  <xs:element name="EPCB">
    <xs:complexType>
      <xs:sequence>
        <xs:element ref="ENLISTMENTS"/>
        <xs:element ref="PROFILING"/>
        <xs:element ref="SOAPS"/>
        <xs:element ref="COURSEWARDS"/>
        <xs:element ref="LABRESULTS"/>
        <xs:element ref="MEDICINES"/>
      </xs:sequence>
      <xs:attribute name="pUsername" use="required"/>
      <xs:attribute name="pPassword" use="required"/>
      <xs:attribute name="pHciAccreNo" use="required"/>
      <xs:attribute name="pEnlistTotalCnt" use="required"/>
      <xs:attribute name="pProfileTotalCnt" use="required"/>
      <xs:attribute name="pSoapTotalCnt" use="required"/>
      <xs:attribute name="pEmrId" use="required"/>
      <xs:attribute name="pCertificationId" use="required"/>
      <xs:attribute name="pHciTransmittalNumber" use="required"/>
    </xs:complexType>
  </xs:element>
  <!-- - Put your DTDDoc comment here. -->
  <xs:element name="ENLISTMENTS">
    <xs:complexType>
      <xs:sequence>
        <xs:element maxOccurs="unbounded" ref="ENLISTMENT"/>
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <!-- - Put your DTDDoc comment here. -->
  <xs:element name="ENLISTMENT">
    <xs:complexType>
      <xs:attribute name="pEClaimId" use="required"/>
      <xs:attribute name="pEClaimsTransmittalId" use="required"/>
      <xs:attribute name="pHciCaseNo" use="required"/>
      <xs:attribute name="pHciTransNo" use="required"/>
      <xs:attribute name="pEffYear" use="required"/>
      <xs:attribute name="pEnlistStat" use="required"/>
      <xs:attribute name="pEnlistDate" use="required"/>
      <xs:attribute name="pPackageType" use="required">
        <xs:simpleType>
          <xs:restriction base="xs:token">
            <xs:enumeration value="P"/>
            <xs:enumeration value="E"/>
            <xs:enumeration value="A"/>
          </xs:restriction>
        </xs:simpleType>
      </xs:attribute>
      <xs:attribute name="pMemPin" use="required"/>
      <xs:attribute name="pMemFname" use="required"/>
      <xs:attribute name="pMemMname" use="required"/>
      <xs:attribute name="pMemLname" use="required"/>
      <xs:attribute name="pMemExtname" use="required"/>
      <xs:attribute name="pMemDob" use="required"/>
      <xs:attribute name="pMemCat" use="required"/>
      <xs:attribute name="pMemNcat" use="required"/>
      <xs:attribute name="pPatientPin" use="required"/>
      <xs:attribute name="pPatientFname" use="required"/>
      <xs:attribute name="pPatientMname" use="required"/>
      <xs:attribute name="pPatientLname" use="required"/>
      <xs:attribute name="pPatientExtname" use="required"/>
      <xs:attribute name="pPatientType" use="required"/>
      <xs:attribute name="pPatientSex" use="required">
        <xs:simpleType>
          <xs:restriction base="xs:token">
            <xs:enumeration value="M"/>
            <xs:enumeration value="F"/>
          </xs:restriction>
        </xs:simpleType>
      </xs:attribute>
      <xs:attribute name="pPatientContactno" use="required"/>
      <xs:attribute name="pPatientDob" use="required"/>
      <xs:attribute name="pPatientAddbrgy" use="required"/>
      <xs:attribute name="pPatientAddmun" use="required"/>
      <xs:attribute name="pPatientAddprov" use="required"/>
      <xs:attribute name="pPatientAddreg" use="required"/>
      <xs:attribute name="pPatientAddzipcode" use="required"/>
      <xs:attribute name="pCivilStatus" use="required">
        <xs:simpleType>
          <xs:restriction base="xs:token">
            <xs:enumeration value="S"/>
            <xs:enumeration value="M"/>
            <xs:enumeration value="W"/>
            <xs:enumeration value="X"/>
            <xs:enumeration value="A"/>
            <xs:enumeration value="U"/>
          </xs:restriction>
        </xs:simpleType>
      </xs:attribute>
      <xs:attribute name="pWithConsent" use="required">
        <xs:simpleType>
          <xs:restriction base="xs:token">
            <xs:enumeration value="Y"/>
            <xs:enumeration value="N"/>
            <xs:enumeration value="X"/>
          </xs:restriction>
        </xs:simpleType>
      </xs:attribute>
      <xs:attribute name="pWithLoa" use="required">
        <xs:simpleType>
          <xs:restriction base="xs:token">
            <xs:enumeration value="Y"/>
            <xs:enumeration value="N"/>
            <xs:enumeration value="X"/>
          </xs:restriction>
        </xs:simpleType>
      </xs:attribute>
      <xs:attribute name="pWithDisability" use="required">
        <xs:simpleType>
          <xs:restriction base="xs:token">
            <xs:enumeration value="Y"/>
            <xs:enumeration value="N"/>
            <xs:enumeration value="X"/>
          </xs:restriction>
        </xs:simpleType>
      </xs:attribute>
      <xs:attribute name="pDependentType" use="required">
        <xs:simpleType>
          <xs:restriction base="xs:token">
            <xs:enumeration value="S"/>
            <xs:enumeration value="C"/>
            <xs:enumeration value="P"/>
            <xs:enumeration value="X"/>
          </xs:restriction>
        </xs:simpleType>
      </xs:attribute>
      <xs:attribute name="pTransDate" use="required"/>
      <xs:attribute name="pCreatedBy" use="required"/>
      <xs:attribute name="pReportStatus" use="required">
        <xs:simpleType>
          <xs:restriction base="xs:token">
            <xs:enumeration value="U"/>
            <xs:enumeration value="V"/>
            <xs:enumeration value="F"/>
          </xs:restriction>
        </xs:simpleType>
      </xs:attribute>
      <xs:attribute name="pDeficiencyRemarks" use="required"/>
      <xs:attribute name="pAvailFreeService" use="required">
        <xs:simpleType>
          <xs:restriction base="xs:token">
            <xs:enumeration value="Y"/>
            <xs:enumeration value="N"/>
            <xs:enumeration value="X"/>
          </xs:restriction>
        </xs:simpleType>
      </xs:attribute>
    </xs:complexType>
  </xs:element>
  <!-- - Put your DTDDoc comment here. -->
  <xs:element name="PROFILING">
    <xs:complexType>
      <xs:sequence>
        <xs:element maxOccurs="unbounded" ref="PROFILE"/>
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <!-- - Put your DTDDoc comment here. -->
  <xs:element name="PROFILE">
    <xs:complexType>
      <xs:sequence>
        <xs:element ref="OINFO"/>
        <xs:element maxOccurs="unbounded" ref="MEDHIST"/>
        <xs:element maxOccurs="unbounded" ref="MHSPECIFIC"/>
        <xs:element maxOccurs="unbounded" ref="SURGHIST"/>
        <xs:element maxOccurs="unbounded" ref="FAMHIST"/>
        <xs:element maxOccurs="unbounded" ref="FHSPECIFIC"/>
        <xs:element ref="SOCHIST"/>
        <xs:element maxOccurs="unbounded" ref="IMMUNIZATION"/>
        <xs:element ref="MENSHIST"/>
        <xs:element ref="PREGHIST"/>
        <xs:element ref="PEPERT"/>
        <xs:element ref="BLOODTYPE"/>
        <xs:element ref="PEGENSURVEY"/>
        <xs:element maxOccurs="unbounded" ref="PEMISC"/>
        <xs:element maxOccurs="unbounded" ref="PESPECIFIC"/>
        <xs:element maxOccurs="unbounded" ref="DIAGNOSTIC"/>
        <xs:element maxOccurs="unbounded" ref="MANAGEMENT"/>
        <xs:element ref="ADVICE"/>
        <xs:element ref="NCDQANS"/>
      </xs:sequence>
      <xs:attribute name="pHciTransNo" use="required"/>
      <xs:attribute name="pHciCaseNo" use="required"/>
      <xs:attribute name="pPatientPin" use="required"/>
      <xs:attribute name="pPatientType" use="required"/>
      <xs:attribute name="pMemPin" use="required"/>
      <xs:attribute name="pProfDate" use="required"/>
      <xs:attribute name="pRemarks" use="required"/>
      <xs:attribute name="pEffYear" use="required"/>
      <xs:attribute name="pProfileATC" use="required"/>
      <xs:attribute name="pReportStatus" use="required">
        <xs:simpleType>
          <xs:restriction base="xs:token">
            <xs:enumeration value="U"/>
            <xs:enumeration value="V"/>
            <xs:enumeration value="F"/>
          </xs:restriction>
        </xs:simpleType>
      </xs:attribute>
      <xs:attribute name="pDeficiencyRemarks" use="required"/>
    </xs:complexType>
  </xs:element>
  <!-- - Put your DTDDoc comment here. -->
  <xs:element name="OINFO">
    <xs:complexType>
      <xs:attribute name="pPatientPob" use="required"/>
      <xs:attribute name="pPatientAge" use="required"/>
      <xs:attribute name="pPatientOccupation" use="required"/>
      <xs:attribute name="pPatientEducation" use="required"/>
      <xs:attribute name="pPatientReligion" use="required"/>
      <xs:attribute name="pPatientMotherMnln" use="required"/>
      <xs:attribute name="pPatientMotherMnmi" use="required"/>
      <xs:attribute name="pPatientMotherFn" use="required"/>
      <xs:attribute name="pPatientMotherExtn" use="required"/>
      <xs:attribute name="pPatientMotherBday" use="required"/>
      <xs:attribute name="pPatientFatherLn" use="required"/>
      <xs:attribute name="pPatientFatherMi" use="required"/>
      <xs:attribute name="pPatientFatherFn" use="required"/>
      <xs:attribute name="pPatientFatherExtn" use="required"/>
      <xs:attribute name="pPatientFatherBday" use="required"/>
      <xs:attribute name="pReportStatus" use="required">
        <xs:simpleType>
          <xs:restriction base="xs:token">
            <xs:enumeration value="U"/>
            <xs:enumeration value="V"/>
            <xs:enumeration value="F"/>
          </xs:restriction>
        </xs:simpleType>
      </xs:attribute>
      <xs:attribute name="pDeficiencyRemarks" use="required"/>
    </xs:complexType>
  </xs:element>
  <!-- - Put your DTDDoc comment here. -->
  <xs:element name="MEDHIST">
    <xs:complexType>
      <xs:attribute name="pMdiseaseCode" use="required"/>
      <xs:attribute name="pReportStatus" use="required">
        <xs:simpleType>
          <xs:restriction base="xs:token">
            <xs:enumeration value="U"/>
            <xs:enumeration value="V"/>
            <xs:enumeration value="F"/>
          </xs:restriction>
        </xs:simpleType>
      </xs:attribute>
      <xs:attribute name="pDeficiencyRemarks" use="required"/>
    </xs:complexType>
  </xs:element>
  <!-- - Put your DTDDoc comment here. -->
  <xs:element name="MHSPECIFIC">
    <xs:complexType>
      <xs:attribute name="pMdiseaseCode" use="required"/>
      <xs:attribute name="pSpecificDesc" use="required"/>
      <xs:attribute name="pReportStatus" use="required">
        <xs:simpleType>
          <xs:restriction base="xs:token">
            <xs:enumeration value="U"/>
            <xs:enumeration value="V"/>
            <xs:enumeration value="F"/>
          </xs:restriction>
        </xs:simpleType>
      </xs:attribute>
      <xs:attribute name="pDeficiencyRemarks" use="required"/>
    </xs:complexType>
  </xs:element>
  <!-- - Put your DTDDoc comment here. -->
  <xs:element name="SURGHIST">
    <xs:complexType>
      <xs:attribute name="pSurgDesc" use="required"/>
      <xs:attribute name="pSurgDate" use="required"/>
      <xs:attribute name="pReportStatus" use="required">
        <xs:simpleType>
          <xs:restriction base="xs:token">
            <xs:enumeration value="U"/>
            <xs:enumeration value="V"/>
            <xs:enumeration value="F"/>
          </xs:restriction>
        </xs:simpleType>
      </xs:attribute>
      <xs:attribute name="pDeficiencyRemarks" use="required"/>
    </xs:complexType>
  </xs:element>
  <!-- - Put your DTDDoc comment here. -->
  <xs:element name="FAMHIST">
    <xs:complexType>
      <xs:attribute name="pMdiseaseCode" use="required"/>
      <xs:attribute name="pReportStatus" use="required">
        <xs:simpleType>
          <xs:restriction base="xs:token">
            <xs:enumeration value="U"/>
            <xs:enumeration value="V"/>
            <xs:enumeration value="F"/>
          </xs:restriction>
        </xs:simpleType>
      </xs:attribute>
      <xs:attribute name="pDeficiencyRemarks" use="required"/>
    </xs:complexType>
  </xs:element>
  <!-- - Put your DTDDoc comment here. -->
  <xs:element name="FHSPECIFIC">
    <xs:complexType>
      <xs:attribute name="pMdiseaseCode" use="required"/>
      <xs:attribute name="pSpecificDesc" use="required"/>
      <xs:attribute name="pReportStatus" use="required">
        <xs:simpleType>
          <xs:restriction base="xs:token">
            <xs:enumeration value="U"/>
            <xs:enumeration value="V"/>
            <xs:enumeration value="F"/>
          </xs:restriction>
        </xs:simpleType>
      </xs:attribute>
      <xs:attribute name="pDeficiencyRemarks" use="required"/>
    </xs:complexType>
  </xs:element>
  <!-- - Put your DTDDoc comment here. -->
  <xs:element name="SOCHIST">
    <xs:complexType>
      <xs:attribute name="pIsSmoker" use="required"/>
      <xs:attribute name="pNoCigpk" use="required"/>
      <xs:attribute name="pIsAdrinker" use="required"/>
      <xs:attribute name="pNoBottles" use="required"/>
      <xs:attribute name="pIllDrugUser" use="required"/>
      <xs:attribute name="pReportStatus" use="required">
        <xs:simpleType>
          <xs:restriction base="xs:token">
            <xs:enumeration value="U"/>
            <xs:enumeration value="V"/>
            <xs:enumeration value="F"/>
          </xs:restriction>
        </xs:simpleType>
      </xs:attribute>
      <xs:attribute name="pDeficiencyRemarks" use="required"/>
    </xs:complexType>
  </xs:element>
  <!-- - Put your DTDDoc comment here. -->
  <xs:element name="IMMUNIZATION">
    <xs:complexType>
      <xs:attribute name="pChildImmcode" use="required"/>
      <xs:attribute name="pYoungwImmcode" use="required"/>
      <xs:attribute name="pPregwImmcode" use="required"/>
      <xs:attribute name="pElderlyImmcode" use="required"/>
      <xs:attribute name="pOtherImm" use="required"/>
      <xs:attribute name="pReportStatus" use="required">
        <xs:simpleType>
          <xs:restriction base="xs:token">
            <xs:enumeration value="U"/>
            <xs:enumeration value="V"/>
            <xs:enumeration value="F"/>
          </xs:restriction>
        </xs:simpleType>
      </xs:attribute>
      <xs:attribute name="pDeficiencyRemarks" use="required"/>
    </xs:complexType>
  </xs:element>
  <!-- - Put your DTDDoc comment here. -->
  <xs:element name="MENSHIST">
    <xs:complexType>
      <xs:attribute name="pMenarchePeriod" use="required"/>
      <xs:attribute name="pLastMensPeriod" use="required"/>
      <xs:attribute name="pPeriodDuration" use="required"/>
      <xs:attribute name="pMensInterval" use="required"/>
      <xs:attribute name="pPadsPerDay" use="required"/>
      <xs:attribute name="pOnsetSexIc" use="required"/>
      <xs:attribute name="pBirthCtrlMethod" use="required"/>
      <xs:attribute name="pIsMenopause" use="required"/>
      <xs:attribute name="pMenopauseAge" use="required"/>
      <xs:attribute name="pIsApplicable" use="required">
        <xs:simpleType>
          <xs:restriction base="xs:token">
            <xs:enumeration value="Y"/>
            <xs:enumeration value="N"/>
          </xs:restriction>
        </xs:simpleType>
      </xs:attribute>
      <xs:attribute name="pReportStatus" use="required">
        <xs:simpleType>
          <xs:restriction base="xs:token">
            <xs:enumeration value="U"/>
            <xs:enumeration value="V"/>
            <xs:enumeration value="F"/>
          </xs:restriction>
        </xs:simpleType>
      </xs:attribute>
      <xs:attribute name="pDeficiencyRemarks" use="required"/>
    </xs:complexType>
  </xs:element>
  <!-- - Put your DTDDoc comment here. -->
  <xs:element name="PREGHIST">
    <xs:complexType>
      <xs:attribute name="pPregCnt" use="required"/>
      <xs:attribute name="pDeliveryCnt" use="required"/>
      <xs:attribute name="pDeliveryTyp" use="required"/>
      <xs:attribute name="pFullTermCnt" use="required"/>
      <xs:attribute name="pPrematureCnt" use="required"/>
      <xs:attribute name="pAbortionCnt" use="required"/>
      <xs:attribute name="pLivChildrenCnt" use="required"/>
      <xs:attribute name="pWPregIndhyp" use="required"/>
      <xs:attribute name="pWFamPlan" use="required"/>
      <xs:attribute name="pReportStatus" use="required">
        <xs:simpleType>
          <xs:restriction base="xs:token">
            <xs:enumeration value="U"/>
            <xs:enumeration value="V"/>
            <xs:enumeration value="F"/>
          </xs:restriction>
        </xs:simpleType>
      </xs:attribute>
      <xs:attribute name="pDeficiencyRemarks" use="required"/>
    </xs:complexType>
  </xs:element>
  <!-- - Put your DTDDoc comment here. -->
  <xs:element name="BLOODTYPE">
    <xs:complexType>
      <xs:attribute name="pBloodType" use="required"/>
      <xs:attribute name="pBloodRh" use="required"/>
      <xs:attribute name="pReportStatus" use="required">
        <xs:simpleType>
          <xs:restriction base="xs:token">
            <xs:enumeration value="U"/>
            <xs:enumeration value="V"/>
            <xs:enumeration value="F"/>
          </xs:restriction>
        </xs:simpleType>
      </xs:attribute>
      <xs:attribute name="pDeficiencyRemarks" use="required"/>
    </xs:complexType>
  </xs:element>
  <!-- - Put your DTDDoc comment here. -->
  <xs:element name="PEGENSURVEY">
    <xs:complexType>
      <xs:attribute name="pGenSurveyId" use="required"/>
      <xs:attribute name="pGenSurveyRem" use="required"/>
      <xs:attribute name="pReportStatus" use="required">
        <xs:simpleType>
          <xs:restriction base="xs:token">
            <xs:enumeration value="U"/>
            <xs:enumeration value="V"/>
            <xs:enumeration value="F"/>
          </xs:restriction>
        </xs:simpleType>
      </xs:attribute>
      <xs:attribute name="pDeficiencyRemarks" use="required"/>
    </xs:complexType>
  </xs:element>
  <!-- - Put your DTDDoc comment here. -->
  <xs:element name="PEMISC">
    <xs:complexType>
      <xs:attribute name="pSkinId" use="required"/>
      <xs:attribute name="pHeentId" use="required"/>
      <xs:attribute name="pChestId" use="required"/>
      <xs:attribute name="pHeartId" use="required"/>
      <xs:attribute name="pAbdomenId" use="required"/>
      <xs:attribute name="pNeuroId" use="required"/>
      <xs:attribute name="pGuId" use="required"/>
      <xs:attribute name="pRectalId" use="required"/>
      <xs:attribute name="pReportStatus" use="required">
        <xs:simpleType>
          <xs:restriction base="xs:token">
            <xs:enumeration value="U"/>
            <xs:enumeration value="V"/>
            <xs:enumeration value="F"/>
          </xs:restriction>
        </xs:simpleType>
      </xs:attribute>
      <xs:attribute name="pDeficiencyRemarks" use="required"/>
    </xs:complexType>
  </xs:element>
  <!-- - Put your DTDDoc comment here. -->
  <xs:element name="NCDQANS">
    <xs:complexType>
      <xs:attribute name="pQid1_Yn" use="required"/>
      <xs:attribute name="pQid2_Yn" use="required"/>
      <xs:attribute name="pQid3_Yn" use="required"/>
      <xs:attribute name="pQid4_Yn" use="required"/>
      <xs:attribute name="pQid5_Ynx" use="required"/>
      <xs:attribute name="pQid6_Yn" use="required"/>
      <xs:attribute name="pQid7_Yn" use="required"/>
      <xs:attribute name="pQid8_Yn" use="required"/>
      <xs:attribute name="pQid9_Yn" use="required"/>
      <xs:attribute name="pQid10_Yn" use="required"/>
      <xs:attribute name="pQid11_Yn" use="required"/>
      <xs:attribute name="pQid12_Yn" use="required"/>
      <xs:attribute name="pQid13_Yn" use="required"/>
      <xs:attribute name="pQid14_Yn" use="required"/>
      <xs:attribute name="pQid15_Yn" use="required"/>
      <xs:attribute name="pQid16_Yn" use="required"/>
      <xs:attribute name="pQid17_Abcde" use="required"/>
      <xs:attribute name="pQid18_Yn" use="required"/>
      <xs:attribute name="pQid19_Yn" use="required"/>
      <xs:attribute name="pQid19_Fbsmg" use="required"/>
      <xs:attribute name="pQid19_Fbsmmol" use="required"/>
      <xs:attribute name="pQid19_Fbsdate" use="required"/>
      <xs:attribute name="pQid20_Yn" use="required"/>
      <xs:attribute name="pQid20_Choleval" use="required"/>
      <xs:attribute name="pQid20_Choledate" use="required"/>
      <xs:attribute name="pQid21_Yn" use="required"/>
      <xs:attribute name="pQid21_Ketonval" use="required"/>
      <xs:attribute name="pQid21_Ketondate" use="required"/>
      <xs:attribute name="pQid22_Yn" use="required"/>
      <xs:attribute name="pQid22_Proteinval" use="required"/>
      <xs:attribute name="pQid22_Proteindate" use="required"/>
      <xs:attribute name="pQid23_Yn" use="required"/>
      <xs:attribute name="pQid24_Yn" use="required"/>
      <xs:attribute name="pReportStatus" use="required">
        <xs:simpleType>
          <xs:restriction base="xs:token">
            <xs:enumeration value="U"/>
            <xs:enumeration value="V"/>
            <xs:enumeration value="F"/>
          </xs:restriction>
        </xs:simpleType>
      </xs:attribute>
      <xs:attribute name="pDeficiencyRemarks" use="required"/>
    </xs:complexType>
  </xs:element>
  <!-- - Put your DTDDoc comment here. -->
  <xs:element name="SOAPS">
    <xs:complexType>
      <xs:sequence>
        <xs:element maxOccurs="unbounded" ref="SOAP"/>
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <!-- - Put your DTDDoc comment here. -->
  <xs:element name="SOAP">
    <xs:complexType>
      <xs:sequence>
        <xs:element maxOccurs="unbounded" ref="SUBJECTIVE"/>
        <xs:element ref="PEPERT"/>
        <xs:element maxOccurs="unbounded" ref="PEMISC"/>
        <xs:element maxOccurs="unbounded" ref="PESPECIFIC"/>
        <xs:element maxOccurs="unbounded" ref="ICDS"/>
        <xs:element maxOccurs="unbounded" ref="DIAGNOSTIC"/>
        <xs:element maxOccurs="unbounded" ref="MANAGEMENT"/>
        <xs:element ref="ADVICE"/>
      </xs:sequence>
      <xs:attribute name="pHciTransNo" use="required"/>
      <xs:attribute name="pHciCaseNo" use="required"/>
      <xs:attribute name="pPatientPin" use="required"/>
      <xs:attribute name="pPatientType" use="required"/>
      <xs:attribute name="pMemPin" use="required"/>
      <xs:attribute name="pSoapDate" use="required"/>
      <xs:attribute name="pEffYear" use="required"/>
      <xs:attribute name="pSoapATC" use="required"/>
      <xs:attribute name="pReportStatus" use="required">
        <xs:simpleType>
          <xs:restriction base="xs:token">
            <xs:enumeration value="U"/>
            <xs:enumeration value="V"/>
            <xs:enumeration value="F"/>
          </xs:restriction>
        </xs:simpleType>
      </xs:attribute>
      <xs:attribute name="pDeficiencyRemarks" use="required"/>
    </xs:complexType>
  </xs:element>
  <!-- - Put your DTDDoc comment here. -->
  <xs:element name="SUBJECTIVE">
    <xs:complexType>
      <xs:attribute name="pChiefComplaint" use="required"/>
      <xs:attribute name="pIllnessHistory" use="required"/>
      <xs:attribute name="pOtherComplaint" use="required"/>
      <xs:attribute name="pSignsSymptoms" use="required"/>
      <xs:attribute name="pPainSite" use="required"/>
      <xs:attribute name="pReportStatus" use="required">
        <xs:simpleType>
          <xs:restriction base="xs:token">
            <xs:enumeration value="U"/>
            <xs:enumeration value="V"/>
            <xs:enumeration value="F"/>
          </xs:restriction>
        </xs:simpleType>
      </xs:attribute>
      <xs:attribute name="pDeficiencyRemarks" use="required"/>
    </xs:complexType>
  </xs:element>
  <!-- - Put your DTDDoc comment here. -->
  <xs:element name="PEPERT">
    <xs:complexType>
      <xs:attribute name="pSystolic" use="required"/>
      <xs:attribute name="pDiastolic" use="required"/>
      <xs:attribute name="pHr" use="required"/>
      <xs:attribute name="pRr" use="required"/>
      <xs:attribute name="pTemp" use="required"/>
      <xs:attribute name="pHeight" use="required"/>
      <xs:attribute name="pWeight" use="required"/>
      <xs:attribute name="pVision" use="required"/>
      <xs:attribute name="pLength" use="required"/>
      <xs:attribute name="pHeadCirc" use="required"/>
      <xs:attribute name="pReportStatus" use="required">
        <xs:simpleType>
          <xs:restriction base="xs:token">
            <xs:enumeration value="U"/>
            <xs:enumeration value="V"/>
            <xs:enumeration value="F"/>
          </xs:restriction>
        </xs:simpleType>
      </xs:attribute>
      <xs:attribute name="pDeficiencyRemarks" use="required"/>
    </xs:complexType>
  </xs:element>
  <!-- - Put your DTDDoc comment here. -->
  <xs:element name="PESPECIFIC">
    <xs:complexType>
      <xs:attribute name="pSkinRem" use="required"/>
      <xs:attribute name="pHeentRem" use="required"/>
      <xs:attribute name="pChestRem" use="required"/>
      <xs:attribute name="pHeartRem" use="required"/>
      <xs:attribute name="pAbdomenRem" use="required"/>
      <xs:attribute name="pNeuroRem" use="required"/>
      <xs:attribute name="pRectalRem" use="required"/>
      <xs:attribute name="pGuRem" use="required"/>
      <xs:attribute name="pReportStatus" use="required">
        <xs:simpleType>
          <xs:restriction base="xs:token">
            <xs:enumeration value="U"/>
            <xs:enumeration value="V"/>
            <xs:enumeration value="F"/>
          </xs:restriction>
        </xs:simpleType>
      </xs:attribute>
      <xs:attribute name="pDeficiencyRemarks" use="required"/>
    </xs:complexType>
  </xs:element>
  <!-- - Put your DTDDoc comment here. -->
  <xs:element name="ICDS">
    <xs:complexType>
      <xs:attribute name="pIcdCode" use="required"/>
      <xs:attribute name="pReportStatus" use="required">
        <xs:simpleType>
          <xs:restriction base="xs:token">
            <xs:enumeration value="U"/>
            <xs:enumeration value="V"/>
            <xs:enumeration value="F"/>
          </xs:restriction>
        </xs:simpleType>
      </xs:attribute>
      <xs:attribute name="pDeficiencyRemarks" use="required"/>
    </xs:complexType>
  </xs:element>
  <!-- - Put your DTDDoc comment here. -->
  <xs:element name="DIAGNOSTIC">
    <xs:complexType>
      <xs:attribute name="pDiagnosticId" use="required"/>
      <xs:attribute name="pOthRemarks" use="required"/>
      <xs:attribute name="pReportStatus" use="required">
        <xs:simpleType>
          <xs:restriction base="xs:token">
            <xs:enumeration value="U"/>
            <xs:enumeration value="V"/>
            <xs:enumeration value="F"/>
          </xs:restriction>
        </xs:simpleType>
      </xs:attribute>
      <xs:attribute name="pDeficiencyRemarks" use="required"/>
    </xs:complexType>
  </xs:element>
  <!-- - Put your DTDDoc comment here. -->
  <xs:element name="MANAGEMENT">
    <xs:complexType>
      <xs:attribute name="pManagementId" use="required"/>
      <xs:attribute name="pOthRemarks" use="required"/>
      <xs:attribute name="pReportStatus" use="required">
        <xs:simpleType>
          <xs:restriction base="xs:token">
            <xs:enumeration value="U"/>
            <xs:enumeration value="V"/>
            <xs:enumeration value="F"/>
          </xs:restriction>
        </xs:simpleType>
      </xs:attribute>
      <xs:attribute name="pDeficiencyRemarks" use="required"/>
    </xs:complexType>
  </xs:element>
  <!-- - Put your DTDDoc comment here. -->
  <xs:element name="ADVICE">
    <xs:complexType>
      <xs:attribute name="pRemarks" use="required"/>
      <xs:attribute name="pReportStatus" use="required"/>
      <xs:attribute name="pDeficiencyRemarks" use="required"/>
    </xs:complexType>
  </xs:element>
  <!-- FOR APPROVAL XML FORMAT -->
  <!-- Put your DTDDoc comment here. -->
  <xs:element name="MEDICINES">
    <xs:complexType>
      <xs:sequence>
        <xs:element maxOccurs="unbounded" ref="MEDICINE"/>
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <!-- - Put your DTDDoc comment here. -->
  <xs:element name="MEDICINE">
    <xs:complexType>
      <xs:attribute name="pHciCaseNo" use="required"/>
      <xs:attribute name="pHciTransNo" use="required"/>
      <xs:attribute name="pDrugCode" use="required"/>
      <xs:attribute name="pGenericName" use="required"/>
      <xs:attribute name="pGenericCode" use="required"/>
      <xs:attribute name="pSaltCode" use="required"/>
      <xs:attribute name="pStrengthCode" use="required"/>
      <xs:attribute name="pFormCode" use="required"/>
      <xs:attribute name="pUnitCode" use="required"/>
      <xs:attribute name="pPackageCode" use="required"/>
      <xs:attribute name="pRoute" use="required"/>
      <xs:attribute name="pQuantity" use="required"/>
      <xs:attribute name="pActualUnitPrice" use="required"/>
      <xs:attribute name="pCoPayment" use="required"/>
      <xs:attribute name="pTotalAmtPrice" use="required"/>
      <xs:attribute name="pInstructionQuantity" use="required"/>
      <xs:attribute name="pInstructionStrength" use="required"/>
      <xs:attribute name="pInstructionFrequency" use="required"/>
      <xs:attribute name="pPrescPhysician" use="required"/>
      <xs:attribute name="pIsApplicable" use="required">
        <xs:simpleType>
          <xs:restriction base="xs:token">
            <xs:enumeration value="Y"/>
            <xs:enumeration value="N"/>
          </xs:restriction>
        </xs:simpleType>
      </xs:attribute>
      <xs:attribute name="pDateAdded" use="required"/>
      <xs:attribute name="pModule" use="required"/>
      <xs:attribute name="pReportStatus" use="required">
        <xs:simpleType>
          <xs:restriction base="xs:token">
            <xs:enumeration value="U"/>
            <xs:enumeration value="V"/>
            <xs:enumeration value="F"/>
          </xs:restriction>
        </xs:simpleType>
      </xs:attribute>
      <xs:attribute name="pDeficiencyRemarks" use="required"/>
    </xs:complexType>
  </xs:element>
  <!-- - Put your DTDDoc comment here. -->
  <xs:element name="COURSEWARDS">
    <xs:complexType>
      <xs:sequence>
        <xs:element maxOccurs="unbounded" ref="COURSEWARD"/>
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:element name="COURSEWARD">
    <xs:complexType>
      <xs:attribute name="pHciCaseNo" use="required"/>
      <xs:attribute name="pHciTransNo" use="required"/>
      <xs:attribute name="pDateAction" use="required"/>
      <xs:attribute name="pDoctorsAction" use="required"/>
      <xs:attribute name="pReportStatus" use="required">
        <xs:simpleType>
          <xs:restriction base="xs:token">
            <xs:enumeration value="U"/>
            <xs:enumeration value="V"/>
            <xs:enumeration value="F"/>
          </xs:restriction>
        </xs:simpleType>
      </xs:attribute>
      <xs:attribute name="pDeficiencyRemarks" use="required"/>
    </xs:complexType>
  </xs:element>
  <!-- - Put your DTDDoc comment here. -->
  <xs:element name="LABRESULTS">
    <xs:complexType>
      <xs:sequence>
        <xs:element maxOccurs="unbounded" ref="LABRESULT"/>
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <!-- - Put your DTDDoc comment here. -->
  <xs:element name="LABRESULT">
    <xs:complexType>
      <xs:sequence>
        <xs:element maxOccurs="unbounded" ref="CBC"/>
        <xs:element maxOccurs="unbounded" ref="URINALYSIS"/>
        <xs:element maxOccurs="unbounded" ref="CHESTXRAY"/>
        <xs:element maxOccurs="unbounded" ref="SPUTUM"/>
        <xs:element maxOccurs="unbounded" ref="LIPIDPROF"/>
        <xs:element maxOccurs="unbounded" ref="FBS"/>
        <xs:element maxOccurs="unbounded" ref="ECG"/>
        <xs:element maxOccurs="unbounded" ref="FECALYSIS"/>
        <xs:element maxOccurs="unbounded" ref="PAPSSMEAR"/>
        <xs:element maxOccurs="unbounded" ref="OGTT"/>
      </xs:sequence>
      <xs:attribute name="pHciCaseNo" use="required"/>
      <xs:attribute name="pPatientPin" use="required"/>
      <xs:attribute name="pPatientType" use="required"/>
      <xs:attribute name="pMemPin" use="required"/>
      <xs:attribute name="pEffYear" use="required"/>
    </xs:complexType>
  </xs:element>
  <!-- - Put your DTDDoc comment here. -->
  <xs:element name="CBC">
    <xs:complexType>
      <xs:attribute name="pHciTransNo" use="required"/>
      <xs:attribute name="pReferralFacility" use="required"/>
      <xs:attribute name="pLabDate" use="required"/>
      <xs:attribute name="pHematocrit" use="required"/>
      <xs:attribute name="pHemoglobinG" use="required"/>
      <xs:attribute name="pHemoglobinMmol" use="required"/>
      <xs:attribute name="pMhcPg" use="required"/>
      <xs:attribute name="pMhcFmol" use="required"/>
      <xs:attribute name="pMchcGhb" use="required"/>
      <xs:attribute name="pMchcMmol" use="required"/>
      <xs:attribute name="pMcvUm" use="required"/>
      <xs:attribute name="pMcvFl" use="required"/>
      <xs:attribute name="pWbc1000" use="required"/>
      <xs:attribute name="pWbc10" use="required"/>
      <xs:attribute name="pMyelocyte" use="required"/>
      <xs:attribute name="pNeutrophilsBnd" use="required"/>
      <xs:attribute name="pNeutrophilsSeg" use="required"/>
      <xs:attribute name="pLymphocytes" use="required"/>
      <xs:attribute name="pMonocytes" use="required"/>
      <xs:attribute name="pEosinophils" use="required"/>
      <xs:attribute name="pBasophils" use="required"/>
      <xs:attribute name="pPlatelet" use="required"/>
      <xs:attribute name="pDateAdded" use="required"/>
      <xs:attribute name="pIsApplicable" use="required">
        <xs:simpleType>
          <xs:restriction base="xs:token">
            <xs:enumeration value="Y"/>
            <xs:enumeration value="N"/>
          </xs:restriction>
        </xs:simpleType>
      </xs:attribute>
      <xs:attribute name="pModule" use="required"/>
      <xs:attribute name="pDiagnosticLabFee" use="required"/>
      <xs:attribute name="pCoPay" use="required"/>
      <xs:attribute name="pReportStatus" use="required">
        <xs:simpleType>
          <xs:restriction base="xs:token">
            <xs:enumeration value="U"/>
            <xs:enumeration value="V"/>
            <xs:enumeration value="F"/>
          </xs:restriction>
        </xs:simpleType>
      </xs:attribute>
      <xs:attribute name="pDeficiencyRemarks" use="required"/>
    </xs:complexType>
  </xs:element>
  <!-- - Put your DTDDoc comment here. -->
  <xs:element name="URINALYSIS">
    <xs:complexType>
      <xs:attribute name="pHciTransNo" use="required"/>
      <xs:attribute name="pReferralFacility" use="required"/>
      <xs:attribute name="pLabDate" use="required"/>
      <xs:attribute name="pGravity" use="required"/>
      <xs:attribute name="pAppearance" use="required"/>
      <xs:attribute name="pColor" use="required"/>
      <xs:attribute name="pGlucose" use="required"/>
      <xs:attribute name="pProteins" use="required"/>
      <xs:attribute name="pKetones" use="required"/>
      <xs:attribute name="pPh" use="required"/>
      <xs:attribute name="pRbCells" use="required"/>
      <xs:attribute name="pWbCells" use="required"/>
      <xs:attribute name="pBacteria" use="required"/>
      <xs:attribute name="pCrystals" use="required"/>
      <xs:attribute name="pBladderCell" use="required"/>
      <xs:attribute name="pSquamousCell" use="required"/>
      <xs:attribute name="pTubularCell" use="required"/>
      <xs:attribute name="pBroadCasts" use="required"/>
      <xs:attribute name="pEpithelialCast" use="required"/>
      <xs:attribute name="pGranularCast" use="required"/>
      <xs:attribute name="pHyalineCast" use="required"/>
      <xs:attribute name="pRbcCast" use="required"/>
      <xs:attribute name="pWaxyCast" use="required"/>
      <xs:attribute name="pWcCast" use="required"/>
      <xs:attribute name="pAlbumin" use="required"/>
      <xs:attribute name="pPusCells" use="required"/>
      <xs:attribute name="pDateAdded" use="required"/>
      <xs:attribute name="pIsApplicable" use="required">
        <xs:simpleType>
          <xs:restriction base="xs:token">
            <xs:enumeration value="Y"/>
            <xs:enumeration value="N"/>
          </xs:restriction>
        </xs:simpleType>
      </xs:attribute>
      <xs:attribute name="pModule" use="required"/>
      <xs:attribute name="pDiagnosticLabFee" use="required"/>
      <xs:attribute name="pCoPay" use="required"/>
      <xs:attribute name="pReportStatus" use="required">
        <xs:simpleType>
          <xs:restriction base="xs:token">
            <xs:enumeration value="U"/>
            <xs:enumeration value="V"/>
            <xs:enumeration value="F"/>
          </xs:restriction>
        </xs:simpleType>
      </xs:attribute>
      <xs:attribute name="pDeficiencyRemarks" use="required"/>
    </xs:complexType>
  </xs:element>
  <!-- - Put your DTDDoc comment here. -->
  <xs:element name="CHESTXRAY">
    <xs:complexType>
      <xs:attribute name="pHciTransNo" use="required"/>
      <xs:attribute name="pReferralFacility" use="required"/>
      <xs:attribute name="pLabDate" use="required"/>
      <xs:attribute name="pFindings" use="required"/>
      <xs:attribute name="pRemarksFindings" use="required"/>
      <xs:attribute name="pObservation" use="required"/>
      <xs:attribute name="pRemarksObservation" use="required"/>
      <xs:attribute name="pDateAdded" use="required"/>
      <xs:attribute name="pIsApplicable" use="required">
        <xs:simpleType>
          <xs:restriction base="xs:token">
            <xs:enumeration value="Y"/>
            <xs:enumeration value="N"/>
          </xs:restriction>
        </xs:simpleType>
      </xs:attribute>
      <xs:attribute name="pModule" use="required"/>
      <xs:attribute name="pDiagnosticLabFee" use="required"/>
      <xs:attribute name="pCoPay" use="required"/>
      <xs:attribute name="pReportStatus" use="required">
        <xs:simpleType>
          <xs:restriction base="xs:token">
            <xs:enumeration value="U"/>
            <xs:enumeration value="V"/>
            <xs:enumeration value="F"/>
          </xs:restriction>
        </xs:simpleType>
      </xs:attribute>
      <xs:attribute name="pDeficiencyRemarks" use="required"/>
    </xs:complexType>
  </xs:element>
  <!-- - Put your DTDDoc comment here. -->
  <xs:element name="SPUTUM">
    <xs:complexType>
      <xs:attribute name="pHciTransNo" use="required"/>
      <xs:attribute name="pReferralFacility" use="required"/>
      <xs:attribute name="pLabDate" use="required"/>
      <xs:attribute name="pDataCollection" use="required">
        <xs:simpleType>
          <xs:restriction base="xs:token">
            <xs:enumeration value="1"/>
            <xs:enumeration value="2"/>
            <xs:enumeration value="3"/>
            <xs:enumeration value="X"/>
          </xs:restriction>
        </xs:simpleType>
      </xs:attribute>
      <xs:attribute name="pFindings" use="required"/>
      <xs:attribute name="pRemarks" use="required"/>
      <xs:attribute name="pNoPlusses" use="required"/>
      <xs:attribute name="pDateAdded" use="required"/>
      <xs:attribute name="pIsApplicable" use="required">
        <xs:simpleType>
          <xs:restriction base="xs:token">
            <xs:enumeration value="Y"/>
            <xs:enumeration value="N"/>
          </xs:restriction>
        </xs:simpleType>
      </xs:attribute>
      <xs:attribute name="pModule" use="required"/>
      <xs:attribute name="pDiagnosticLabFee" use="required"/>
      <xs:attribute name="pCoPay" use="required"/>
      <xs:attribute name="pReportStatus" use="required">
        <xs:simpleType>
          <xs:restriction base="xs:token">
            <xs:enumeration value="U"/>
            <xs:enumeration value="V"/>
            <xs:enumeration value="F"/>
          </xs:restriction>
        </xs:simpleType>
      </xs:attribute>
      <xs:attribute name="pDeficiencyRemarks" use="required"/>
    </xs:complexType>
  </xs:element>
  <!-- - Put your DTDDoc comment here. -->
  <xs:element name="LIPIDPROF">
    <xs:complexType>
      <xs:attribute name="pHciTransNo" use="required"/>
      <xs:attribute name="pReferralFacility" use="required"/>
      <xs:attribute name="pLabDate" use="required"/>
      <xs:attribute name="pLdl" use="required"/>
      <xs:attribute name="pHdl" use="required"/>
      <xs:attribute name="pTotal" use="required"/>
      <xs:attribute name="pCholesterol" use="required"/>
      <xs:attribute name="pTriglycerides" use="required"/>
      <xs:attribute name="pDateAdded" use="required"/>
      <xs:attribute name="pIsApplicable" use="required">
        <xs:simpleType>
          <xs:restriction base="xs:token">
            <xs:enumeration value="Y"/>
            <xs:enumeration value="N"/>
          </xs:restriction>
        </xs:simpleType>
      </xs:attribute>
      <xs:attribute name="pModule" use="required"/>
      <xs:attribute name="pDiagnosticLabFee" use="required"/>
      <xs:attribute name="pCoPay" use="required"/>
      <xs:attribute name="pReportStatus" use="required">
        <xs:simpleType>
          <xs:restriction base="xs:token">
            <xs:enumeration value="U"/>
            <xs:enumeration value="V"/>
            <xs:enumeration value="F"/>
          </xs:restriction>
        </xs:simpleType>
      </xs:attribute>
      <xs:attribute name="pDeficiencyRemarks" use="required"/>
    </xs:complexType>
  </xs:element>
  <!-- - Put your DTDDoc comment here. -->
  <xs:element name="FBS">
    <xs:complexType>
      <xs:attribute name="pHciTransNo" use="required"/>
      <xs:attribute name="pReferralFacility" use="required"/>
      <xs:attribute name="pLabDate" use="required"/>
      <xs:attribute name="pGlucoseMg" use="required"/>
      <xs:attribute name="pGlucoseMmol" use="required"/>
      <xs:attribute name="pDateAdded" use="required"/>
      <xs:attribute name="pIsApplicable" use="required">
        <xs:simpleType>
          <xs:restriction base="xs:token">
            <xs:enumeration value="Y"/>
            <xs:enumeration value="N"/>
          </xs:restriction>
        </xs:simpleType>
      </xs:attribute>
      <xs:attribute name="pModule" use="required"/>
      <xs:attribute name="pDiagnosticLabFee" use="required"/>
      <xs:attribute name="pCoPay" use="required"/>
      <xs:attribute name="pReportStatus" use="required">
        <xs:simpleType>
          <xs:restriction base="xs:token">
            <xs:enumeration value="U"/>
            <xs:enumeration value="V"/>
            <xs:enumeration value="F"/>
          </xs:restriction>
        </xs:simpleType>
      </xs:attribute>
      <xs:attribute name="pDeficiencyRemarks" use="required"/>
    </xs:complexType>
  </xs:element>
  <!-- - Put your DTDDoc comment here. -->
  <xs:element name="ECG">
    <xs:complexType>
      <xs:attribute name="pHciTransNo" use="required"/>
      <xs:attribute name="pReferralFacility" use="required"/>
      <xs:attribute name="pLabDate" use="required"/>
      <xs:attribute name="pFindings" use="required"/>
      <xs:attribute name="pRemarks" use="required"/>
      <xs:attribute name="pDateAdded" use="required"/>
      <xs:attribute name="pIsApplicable" use="required">
        <xs:simpleType>
          <xs:restriction base="xs:token">
            <xs:enumeration value="Y"/>
            <xs:enumeration value="N"/>
          </xs:restriction>
        </xs:simpleType>
      </xs:attribute>
      <xs:attribute name="pModule" use="required"/>
      <xs:attribute name="pDiagnosticLabFee" use="required"/>
      <xs:attribute name="pCoPay" use="required"/>
      <xs:attribute name="pReportStatus" use="required">
        <xs:simpleType>
          <xs:restriction base="xs:token">
            <xs:enumeration value="U"/>
            <xs:enumeration value="V"/>
            <xs:enumeration value="F"/>
          </xs:restriction>
        </xs:simpleType>
      </xs:attribute>
      <xs:attribute name="pDeficiencyRemarks" use="required"/>
    </xs:complexType>
  </xs:element>
  <!-- - Put your DTDDoc comment here. -->
  <xs:element name="FECALYSIS">
    <xs:complexType>
      <xs:attribute name="pHciTransNo" use="required"/>
      <xs:attribute name="pReferralFacility" use="required"/>
      <xs:attribute name="pLabDate" use="required"/>
      <xs:attribute name="pColor" use="required"/>
      <xs:attribute name="pConsistency" use="required"/>
      <xs:attribute name="pRbc" use="required"/>
      <xs:attribute name="pWbc" use="required"/>
      <xs:attribute name="pOva" use="required"/>
      <xs:attribute name="pParasite" use="required"/>
      <xs:attribute name="pBlood" use="required"/>
      <xs:attribute name="pOccultBlood" use="required"/>
      <xs:attribute name="pPusCells" use="required"/>
      <xs:attribute name="pDateAdded" use="required"/>
      <xs:attribute name="pIsApplicable" use="required">
        <xs:simpleType>
          <xs:restriction base="xs:token">
            <xs:enumeration value="Y"/>
            <xs:enumeration value="N"/>
          </xs:restriction>
        </xs:simpleType>
      </xs:attribute>
      <xs:attribute name="pModule" use="required"/>
      <xs:attribute name="pDiagnosticLabFee" use="required"/>
      <xs:attribute name="pCoPay" use="required"/>
      <xs:attribute name="pReportStatus" use="required">
        <xs:simpleType>
          <xs:restriction base="xs:token">
            <xs:enumeration value="U"/>
            <xs:enumeration value="V"/>
            <xs:enumeration value="F"/>
          </xs:restriction>
        </xs:simpleType>
      </xs:attribute>
      <xs:attribute name="pDeficiencyRemarks" use="required"/>
    </xs:complexType>
  </xs:element>
  <!-- - Put your DTDDoc comment here. -->
  <xs:element name="PAPSSMEAR">
    <xs:complexType>
      <xs:attribute name="pHciTransNo" use="required"/>
      <xs:attribute name="pReferralFacility" use="required"/>
      <xs:attribute name="pLabDate" use="required"/>
      <xs:attribute name="pFindings" use="required"/>
      <xs:attribute name="pImpression" use="required"/>
      <xs:attribute name="pDateAdded" use="required"/>
      <xs:attribute name="pIsApplicable" use="required">
        <xs:simpleType>
          <xs:restriction base="xs:token">
            <xs:enumeration value="Y"/>
            <xs:enumeration value="N"/>
            <xs:enumeration value="W"/>
          </xs:restriction>
        </xs:simpleType>
      </xs:attribute>
      <xs:attribute name="pModule" use="required"/>
      <xs:attribute name="pDiagnosticLabFee" use="required"/>
      <xs:attribute name="pCoPay" use="required"/>
      <xs:attribute name="pReportStatus" use="required">
        <xs:simpleType>
          <xs:restriction base="xs:token">
            <xs:enumeration value="U"/>
            <xs:enumeration value="V"/>
            <xs:enumeration value="F"/>
          </xs:restriction>
        </xs:simpleType>
      </xs:attribute>
      <xs:attribute name="pDeficiencyRemarks" use="required"/>
    </xs:complexType>
  </xs:element>
  <!-- - Put your DTDDoc comment here. -->
  <xs:element name="OGTT">
    <xs:complexType>
      <xs:attribute name="pHciTransNo" use="required"/>
      <xs:attribute name="pReferralFacility" use="required"/>
      <xs:attribute name="pLabDate" use="required"/>
      <xs:attribute name="pExamFastingMg" use="required"/>
      <xs:attribute name="pExamFastingMmol" use="required"/>
      <xs:attribute name="pExamOgttOneHrMg" use="required"/>
      <xs:attribute name="pExamOgttOneHrMmol" use="required"/>
      <xs:attribute name="pExamOgttTwoHrMg" use="required"/>
      <xs:attribute name="pExamOgttTwoHrMmol" use="required"/>
      <xs:attribute name="pDateAdded" use="required"/>
      <xs:attribute name="pIsApplicable" use="required">
        <xs:simpleType>
          <xs:restriction base="xs:token">
            <xs:enumeration value="Y"/>
            <xs:enumeration value="N"/>
            <xs:enumeration value="W"/>
          </xs:restriction>
        </xs:simpleType>
      </xs:attribute>
      <xs:attribute name="pModule" use="required"/>
      <xs:attribute name="pDiagnosticLabFee" use="required"/>
      <xs:attribute name="pCoPay" use="required"/>
      <xs:attribute name="pReportStatus" use="required">
        <xs:simpleType>
          <xs:restriction base="xs:token">
            <xs:enumeration value="U"/>
            <xs:enumeration value="V"/>
            <xs:enumeration value="F"/>
          </xs:restriction>
        </xs:simpleType>
      </xs:attribute>
      <xs:attribute name="pDeficiencyRemarks" use="required"/>
    </xs:complexType>
  </xs:element>
</xs:schema>
