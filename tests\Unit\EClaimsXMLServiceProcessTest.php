<?php

namespace Tests\Unit;

use Tests\TestCase;
use App\Repositories\EClaimsRepository;
use App\Services\ExternalAPIService;
use App\Services\PhilhealthEncryptionService;
use App\Services\Validators\eClaimsValidatorService;
use App\Services\XMLServices\eClaimsXMLService;
use PHPUnit\Framework\Attributes\Test;

class EClaimsXMLServiceProcessTest extends TestCase
{
    protected $eClaimsRepositoryMock;
    protected $eClaimsValidatorMock;
    protected $encryptionServiceMock;
    protected $externalAPIServiceMock;
    protected $service;

    protected function setUp(): void
    {
        parent::setUp();

        // Create mocks for dependencies
        $this->eClaimsRepositoryMock = $this->createMock(EClaimsRepository::class);
        $this->eClaimsValidatorMock = $this->createMock(eClaimsValidatorService::class);
        $this->encryptionServiceMock = $this->createMock(PhilhealthEncryptionService::class);
        $this->externalAPIServiceMock = $this->createMock(ExternalAPIService::class);

        // Instantiate the service with mocked dependencies
        $this->service = new eClaimsXMLService(
            $this->eClaimsRepositoryMock,
            $this->eClaimsValidatorMock,
            $this->encryptionServiceMock,
            $this->externalAPIServiceMock
        );
    }

    // /** @test */
    #[Test]
    public function it_processes_an_eclaims_xml_correctly()
    {
        // Arrange
        $enccode = '{"enccode" : "ADM1000394Jan062025160825"}';
        $xml = '<eClaims>Valid Data</eClaims>';
        $encryptedXml = 'ENCRYPTED_XML';
        $apiResponse = ['status' => 'success'];
        $validate_local_response = ['{
    "message": "eClaims XML is valid against DTD!"
}'
        ];

        // Mock repository to return XML data
        $this->eClaimsRepositoryMock
            ->expects($this->once())
            ->method('geteClaimsData')
            ->with($enccode)
            ->willReturn($xml);

        // Mock validator to return true (valid XML)
        $this->eClaimsValidatorMock
            ->expects($this->once())
            ->method('validateXML')
            ->with($xml)
            ->willReturn($validate_local_response);

        // Mock encryption service
        $this->encryptionServiceMock
            ->expects($this->once())
            ->method('encryptMessage')
            ->with($xml)
            ->willReturn($encryptedXml);

        // Mock external API call
        $this->externalAPIServiceMock
            ->expects($this->once())
            ->method('post')
            ->with('eClaimsFileCheck', $encryptedXml)
            ->willReturn($apiResponse);

        // Act
        $result = $this->service->eClaimsFileCheck($enccode);

        // Assert
        $this->assertEquals($apiResponse, $result);
    }

    // /** @test */
    #[Test]
    public function it_throws_exception_if_xml_is_invalid()
    {
        // Arrange
        $enccode = '{"enccode" : "ADM1000394Jan062025160825"}';
        $xml = '<eClaims>inValid Data</eClaims>';
        $expected = ['{
    "error": "eClaims XML validation failed",
    "details": []}'
        ];

        // Mock repository response
        $this->eClaimsRepositoryMock
            ->expects($this->once())
            ->method('geteClaimsData')
            ->with($enccode)
            ->willReturn($xml);

        // Mock validator to return false (invalid XML)
        $this->eClaimsValidatorMock
            ->expects($this->once())
            ->method('validateXML')
            ->with($xml)
            ->willReturn($expected);

        // Act & Assert
        $this->expectException(\Exception::class);
        // $this->expectExceptionMessage("Invalid XML, cannot process.");

        $this->service->eClaimsFileCheck($enccode);
    }

    protected function tearDown(): void
    {
        // Mockery::close();
        parent::tearDown();
    }
}
