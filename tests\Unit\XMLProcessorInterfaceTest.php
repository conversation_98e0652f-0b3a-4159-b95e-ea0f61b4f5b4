<?php

namespace Tests\Unit;

use App\Interfaces\Contracts\XmlProcessorInterface;
use Tests\TestCase;

class XmlProcessorInterfaceTest extends TestCase
{
    public function test_interface_has_required_methods()
    {
        $interface = new \ReflectionClass(XmlProcessorInterface::class);
        $methods = [
            'retrieveFromDatabase',
            'validateLocally',
            'encryptPayload',
            'validateRemotely',
            'postToApi'
        ];

        foreach ($methods as $method) {
            $this->assertTrue($interface->hasMethod($method), "Missing method: $method");
        }
    }
}
