<?xml version="1.0" encoding="UTF-8"?>
<!--
  Philippine Health Insurance Corporation
  Electronic Statement of Account Data Type Definition Version 0.1
  0.1 : 2022-08-19 12:24pm : Initial
  0.2 : 2023-02-13 03:51pm : Revised
      0.3	: 2023-05-30 09:14am : Added pCategory in ItemizedBillingItem
      0.4	: 2023-08-14 05:17pm : Changed pActualCharges to pChargesNetOfApplicableVat
      0.5	: 2025-02-17 01:08pm : Added the specs for the "Others" element and "Others" category
-->
<xs:schema xmlns:xs="http://www.w3.org/2001/XMLSchema" elementFormDefault="qualified">
  <xs:element name="eSOA">
    <xs:complexType>
      <xs:sequence>
        <xs:element ref="SummaryOfFees"/>
        <xs:element ref="ProfessionalFees"/>
        <xs:element ref="ItemizedBillingItems"/>
      </xs:sequence>
      <xs:attribute name="pHciPan" use="required"/>
      <xs:attribute name="pHciTransmittalId" use="required"/>
    </xs:complexType>
  </xs:element>
  <xs:element name="SummaryOfFees">
    <xs:complexType>
      <xs:sequence>
        <xs:element ref="RoomAndBoard"/>
        <xs:element ref="DrugsAndMedicine"/>
        <xs:element ref="LaboratoryAndDiagnostic"/>
        <xs:element ref="OperatingRoomFees"/>
        <xs:element ref="MedicalSupplies"/>
        <xs:element ref="Others"/>
        <xs:element ref="PhilHealth"/>
        <xs:element ref="Balance"/>
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:element name="ProfessionalFees">
    <xs:complexType>
      <xs:sequence>
        <xs:element minOccurs="0" maxOccurs="unbounded" ref="ProfessionalFee"/>
        <xs:element ref="PhilHealth"/>
        <xs:element ref="Balance"/>
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:element name="ItemizedBillingItems">
    <xs:complexType>
      <xs:sequence>
        <xs:element maxOccurs="unbounded" ref="ItemizedBillingItem"/>
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:element name="PhilHealth">
    <xs:complexType>
      <xs:attribute name="pTotalCaseRateAmount" use="required"/>
    </xs:complexType>
  </xs:element>
  <xs:element name="Balance">
    <xs:complexType>
      <xs:attribute name="pAmount" use="required"/>
    </xs:complexType>
  </xs:element>
  <xs:element name="OtherFundSource">
    <xs:complexType>
      <xs:attribute name="pDescription" use="required"/>
      <xs:attribute name="pAmount" use="required"/>
    </xs:complexType>
  </xs:element>
  <xs:element name="SummaryOfFee">
    <xs:complexType>
      <xs:attribute name="pChargesNetOfApplicableVat" use="required"/>
      <xs:attribute name="pSeniorCitizenDiscount" use="required"/>
      <xs:attribute name="pPWDDiscount" use="required"/>
      <xs:attribute name="pPCSO" use="required"/>
      <xs:attribute name="pDSWD" use="required"/>
      <xs:attribute name="pDOHMAP" use="required"/>
      <xs:attribute name="pHMO" use="required"/>
    </xs:complexType>
  </xs:element>
  <xs:element name="RoomAndBoard">
    <xs:complexType>
      <xs:sequence>
        <xs:element ref="SummaryOfFee"/>
        <xs:element minOccurs="0" maxOccurs="unbounded" ref="OtherFundSource"/>
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:element name="DrugsAndMedicine">
    <xs:complexType>
      <xs:sequence>
        <xs:element ref="SummaryOfFee"/>
        <xs:element minOccurs="0" maxOccurs="unbounded" ref="OtherFundSource"/>
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:element name="LaboratoryAndDiagnostic">
    <xs:complexType>
      <xs:sequence>
        <xs:element ref="SummaryOfFee"/>
        <xs:element minOccurs="0" maxOccurs="unbounded" ref="OtherFundSource"/>
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:element name="OperatingRoomFees">
    <xs:complexType>
      <xs:sequence>
        <xs:element ref="SummaryOfFee"/>
        <xs:element minOccurs="0" maxOccurs="unbounded" ref="OtherFundSource"/>
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:element name="MedicalSupplies">
    <xs:complexType>
      <xs:sequence>
        <xs:element ref="SummaryOfFee"/>
        <xs:element minOccurs="0" maxOccurs="unbounded" ref="OtherFundSource"/>
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:element name="Others">
    <xs:complexType>
      <xs:sequence>
        <xs:element ref="SummaryOfFee"/>
        <xs:element minOccurs="0" maxOccurs="unbounded" ref="OtherFundSource"/>
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:element name="ProfessionalFee">
    <xs:complexType>
      <xs:sequence>
        <xs:element ref="ProfessionalInfo"/>
        <xs:element ref="SummaryOfFee"/>
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:element name="ProfessionalInfo">
    <xs:complexType>
      <xs:attribute name="pPAN" use="required"/>
      <xs:attribute name="pFirstName" use="required"/>
      <xs:attribute name="pMiddleName" use="required"/>
      <xs:attribute name="pLastName" use="required"/>
      <xs:attribute name="pSuffixName" use="required"/>
    </xs:complexType>
  </xs:element>
  <xs:element name="ItemizedBillingItem">
    <xs:complexType>
      <xs:attribute name="pServiceDate" use="required"/>
      <xs:attribute name="pItemCode" use="required"/>
      <xs:attribute name="pItemName" use="required"/>
      <xs:attribute name="pUnitOfMeasurement" use="required"/>
      <xs:attribute name="pUnitPrice" use="required"/>
      <xs:attribute name="pQuantity" use="required"/>
      <xs:attribute name="pTotalAmount" use="required"/>
      <xs:attribute name="pCategory" use="required">
        <xs:simpleType>
          <xs:restriction base="xs:token">
            <xs:enumeration value="RoomAndBoard"/>
            <xs:enumeration value="DrugsAndMedicine"/>
            <xs:enumeration value="LaboratoryAndDiagnostic"/>
            <xs:enumeration value="OperatingRoomFees"/>
            <xs:enumeration value="MedicalSupplies"/>
            <xs:enumeration value="Others"/>
          </xs:restriction>
        </xs:simpleType>
      </xs:attribute>
    </xs:complexType>
  </xs:element>
</xs:schema>
