<?xml version="1.0" encoding="UTF-8"?>
<!--
  Philippine Health Insurance Corporation
  eClaims Document Type Definition Version 1.9
  Version History
  1.9.0 06-23-2017 08:16PM (MSM)
      : removed the ennumerated list values for the pDocumentType attribute for flexibility of adding new elements
  		: added pServiceProvider attribute to the eCLAIMS element to indicate the provider of the system used to encode and submit the e-claims XML file 
  1.8.0 09-19-2016 05:13PM (MSM)
      : added "IMRT" element as additional to the list of supported repetitive procedures
      : added "APR" element to CF2 for saving the data of the Consent to Access Patient Record section of RF2
      : added "pHasAttachedSOA" attribute to CF2 element
      : added new "CATARACTINFO" element to hold info about data like IOL sticker number for cataract operation. The "CATARACT" element will be deprecated later
      : changed the comments for the pThumbMarkedBy attribute
  1.7.6 07-31-2015 10:38PM 
      : remove "()" in this part (BENEFITS)
  1.7.5 revised : 06-23-2015 1:19PM
      : added "P" (Lifetime Member) in the pMemberShipType attribute acceptable values 
      : added "ANR" (Anesthesia Record) & "HDR" (Hemodialysis Record) in the pDocumentType attribute acceptable values
  1.7.4 revised : 11-03-2014 9:52am
      : added "MRF" (PhilHealth Member Registration Form) in the pDocumentType 
  1.7.3 revised : 01-28-2014 9:58am
      : added pPreAuthDate in ZBENEFIT element
      : added pCaseRateAmount in CASERATE element
      : added pDoctorSignDate in PROFESSIONALS element
      : added pPatientType & pIsEmergency in CLAIM element
      : transfer pCataractPreAuth to CATARACT element
  1.7.2 revised : 12-16-2013 5:03pm
      : abstracted the Particulars from CF3 outside
      : added the CF3_OLD and CF3_NEW elements
  1.7.1 revised : 11-04-2013 3:31pm
      : updated the elements based on the claim forms version 11_04_2013
  1.7 revised : 09-24-2013 08:56am
      : major revision to cater all case rate policy
  1.6.1 revised : 01-29-2013 01:29pm
      : added CSF in list of document types
  1.6 revised : 01-25-2013 01:11pm
      : updated the new coding of documents
  1.5 revised : 10-18-2012 10:01am
      : abstracted the Observation Codes from ZBenefit outside
      : added Official Receipt details
  1.4 revised : 07-16-2012 01:40pm
      : added support for observation for z-benefits
  1.3 revised : 06-01-2012 09:04am
      : added elements for document urls
      : added elements for Z Benefit
  1.2 revised : 02-24-2012 06:16pm
      : added code for packages
      : added codes for case-rates
  1.1 revised : 08-19-2011 11:02am
      : added requirements for case rate
  1.0 revised : 11-12-2010 04:42pm
-->
<xs:schema xmlns:xs="http://www.w3.org/2001/XMLSchema" elementFormDefault="qualified">
  <xs:element name="eCLAIMS">
    <xs:complexType>
      <xs:complexContent>
        <xs:extension base="eTRANSMITTAL">
          <xs:attribute name="pUserName" use="required"/>
          <xs:attribute name="pUserPassword" use="required"/>
          <xs:attribute name="pHospitalCode" use="required"/>
          <xs:attribute name="pHospitalEmail" use="required"/>
          <xs:attribute name="pServiceProvider"/>
        </xs:extension>
      </xs:complexContent>
    </xs:complexType>
  </xs:element>
  <xs:complexType name="eTRANSMITTAL">
    <xs:sequence>
      <xs:element ref="eTRANSMITTAL"/>
    </xs:sequence>
  </xs:complexType>
  <xs:element name="eTRANSMITTAL">
    <xs:complexType>
      <xs:sequence>
        <xs:element maxOccurs="unbounded" ref="CLAIM"/>
      </xs:sequence>
      <xs:attribute name="pHospitalTransmittalNo" use="required"/>
      <xs:attribute name="pTotalClaims" use="required"/>
    </xs:complexType>
  </xs:element>
  <xs:element name="CLAIM">
    <xs:complexType>
      <xs:sequence>
        <xs:element ref="CF1"/>
        <xs:element ref="CF2"/>
        <xs:choice>
          <xs:element ref="ALLCASERATE"/>
          <xs:element ref="ZBENEFIT"/>
        </xs:choice>
        <xs:element minOccurs="0" ref="CF3"/>
        <xs:element minOccurs="0" ref="PARTICULARS"/>
        <xs:element minOccurs="0" ref="RECEIPTS"/>
        <xs:element ref="DOCUMENTS"/>
      </xs:sequence>
      <xs:attribute name="pClaimNumber" use="required"/>
      <xs:attribute name="pTrackingNumber" use="required"/>
      <xs:attribute name="pPhilhealthClaimType" use="required">
        <xs:simpleType>
          <xs:restriction base="xs:token">
            <xs:enumeration value="ALL-CASE-RATE"/>
            <xs:enumeration value="Z-BENEFIT"/>
          </xs:restriction>
        </xs:simpleType>
      </xs:attribute>
      <xs:attribute name="pPatientType" use="required">
        <xs:simpleType>
          <xs:restriction base="xs:token">
            <xs:enumeration value="I"/>
            <xs:enumeration value="O"/>
          </xs:restriction>
        </xs:simpleType>
      </xs:attribute>
      <xs:attribute name="pIsEmergency" use="required">
        <xs:simpleType>
          <xs:restriction base="xs:token">
            <xs:enumeration value="Y"/>
            <xs:enumeration value="N"/>
          </xs:restriction>
        </xs:simpleType>
      </xs:attribute>
    </xs:complexType>
  </xs:element>
  <xs:element name="CF1">
    <xs:complexType>
      <xs:attribute name="pMemberPIN" use="required"/>
      <xs:attribute name="pMemberLastName" use="required"/>
      <xs:attribute name="pMemberFirstName" use="required"/>
      <xs:attribute name="pMemberSuffix" use="required"/>
      <xs:attribute name="pMemberMiddleName" use="required"/>
      <xs:attribute name="pMemberBirthDate" use="required"/>
      <xs:attribute name="pMemberShipType" use="required">
        <xs:simpleType>
          <xs:restriction base="xs:token">
            <xs:enumeration value="S"/>
            <xs:enumeration value="G"/>
            <xs:enumeration value="I"/>
            <xs:enumeration value="NS"/>
            <xs:enumeration value="NO"/>
            <xs:enumeration value="PS"/>
            <xs:enumeration value="PG"/>
            <xs:enumeration value="P"/>
          </xs:restriction>
        </xs:simpleType>
      </xs:attribute>
      <xs:attribute name="pMailingAddress" use="required"/>
      <xs:attribute name="pZipCode" use="required"/>
      <xs:attribute name="pMemberSex" use="required">
        <xs:simpleType>
          <xs:restriction base="xs:token">
            <xs:enumeration value="M"/>
            <xs:enumeration value="F"/>
          </xs:restriction>
        </xs:simpleType>
      </xs:attribute>
      <xs:attribute name="pLandlineNo" use="required"/>
      <xs:attribute name="pMobileNo" use="required"/>
      <xs:attribute name="pEmailAddress" use="required"/>
      <xs:attribute name="pPatientIs" use="required">
        <xs:simpleType>
          <xs:restriction base="xs:token">
            <xs:enumeration value="M"/>
            <xs:enumeration value="S"/>
            <xs:enumeration value="C"/>
            <xs:enumeration value="P"/>
          </xs:restriction>
        </xs:simpleType>
      </xs:attribute>
      <xs:attribute name="pPatientPIN" use="required"/>
      <xs:attribute name="pPatientLastName" use="required"/>
      <xs:attribute name="pPatientFirstName" use="required"/>
      <xs:attribute name="pPatientSuffix" use="required"/>
      <xs:attribute name="pPatientMiddleName" use="required"/>
      <xs:attribute name="pPatientBirthDate" use="required"/>
      <xs:attribute name="pPatientSex" use="required">
        <xs:simpleType>
          <xs:restriction base="xs:token">
            <xs:enumeration value="M"/>
            <xs:enumeration value="F"/>
          </xs:restriction>
        </xs:simpleType>
      </xs:attribute>
      <xs:attribute name="pPEN" use="required"/>
      <xs:attribute name="pEmployerName" use="required"/>
    </xs:complexType>
  </xs:element>
  <xs:element name="CF2">
    <xs:complexType>
      <xs:sequence>
        <xs:element ref="DIAGNOSIS"/>
        <xs:element ref="SPECIAL"/>
        <xs:element maxOccurs="unbounded" ref="PROFESSIONALS"/>
        <xs:element ref="CONSUMPTION"/>
        <xs:element minOccurs="0" ref="APR"/>
      </xs:sequence>
      <xs:attribute name="pPatientReferred" use="required">
        <xs:simpleType>
          <xs:restriction base="xs:token">
            <xs:enumeration value="Y"/>
            <xs:enumeration value="N"/>
          </xs:restriction>
        </xs:simpleType>
      </xs:attribute>
      <xs:attribute name="pReferredIHCPAccreCode" use="required"/>
      <xs:attribute name="pAdmissionDate" use="required"/>
      <xs:attribute name="pAdmissionTime" use="required"/>
      <xs:attribute name="pDischargeDate" use="required"/>
      <xs:attribute name="pDischargeTime" use="required"/>
      <xs:attribute name="pDisposition" use="required">
        <xs:simpleType>
          <xs:restriction base="xs:token">
            <xs:enumeration value="I"/>
            <xs:enumeration value="R"/>
            <xs:enumeration value="H"/>
            <xs:enumeration value="A"/>
            <xs:enumeration value="E"/>
            <xs:enumeration value="T"/>
          </xs:restriction>
        </xs:simpleType>
      </xs:attribute>
      <xs:attribute name="pExpiredDate" use="required"/>
      <xs:attribute name="pExpiredTime" use="required"/>
      <xs:attribute name="pReferralIHCPAccreCode" use="required"/>
      <xs:attribute name="pReferralReasons" use="required"/>
      <xs:attribute name="pAccommodationType" use="required">
        <xs:simpleType>
          <xs:restriction base="xs:token">
            <xs:enumeration value="P"/>
            <xs:enumeration value="N"/>
          </xs:restriction>
        </xs:simpleType>
      </xs:attribute>
      <xs:attribute name="pHasAttachedSOA">
        <xs:simpleType>
          <xs:restriction base="xs:token">
            <xs:enumeration value="Y"/>
            <xs:enumeration value="N"/>
          </xs:restriction>
        </xs:simpleType>
      </xs:attribute>
    </xs:complexType>
  </xs:element>
  <xs:element name="DIAGNOSIS">
    <xs:complexType>
      <xs:sequence>
        <xs:element maxOccurs="unbounded" ref="DISCHARGE"/>
      </xs:sequence>
      <xs:attribute name="pAdmissionDiagnosis" use="required"/>
    </xs:complexType>
  </xs:element>
  <xs:element name="DISCHARGE">
    <xs:complexType>
      <xs:choice>
        <xs:sequence>
          <xs:element maxOccurs="unbounded" ref="ICDCODE"/>
          <xs:element minOccurs="0" maxOccurs="unbounded" ref="RVSCODES"/>
        </xs:sequence>
        <xs:sequence>
          <xs:element minOccurs="0" maxOccurs="unbounded" ref="ICDCODE"/>
          <xs:element maxOccurs="unbounded" ref="RVSCODES"/>
        </xs:sequence>
      </xs:choice>
      <xs:attribute name="pDischargeDiagnosis" use="required"/>
    </xs:complexType>
  </xs:element>
  <xs:element name="ICDCODE">
    <xs:complexType>
      <xs:attribute name="pICDCode" use="required"/>
    </xs:complexType>
  </xs:element>
  <xs:element name="RVSCODES">
    <xs:complexType>
      <xs:attribute name="pRelatedProcedure" use="required"/>
      <xs:attribute name="pRVSCode" use="required"/>
      <xs:attribute name="pProcedureDate" use="required"/>
      <xs:attribute name="pLaterality" use="required">
        <xs:simpleType>
          <xs:restriction base="xs:token">
            <xs:enumeration value="L"/>
            <xs:enumeration value="R"/>
            <xs:enumeration value="B"/>
            <xs:enumeration value="N"/>
          </xs:restriction>
        </xs:simpleType>
      </xs:attribute>
    </xs:complexType>
  </xs:element>
  <xs:element name="SPECIAL">
    <xs:complexType>
      <xs:sequence>
        <xs:element minOccurs="0" ref="PROCEDURES"/>
        <xs:element minOccurs="0" ref="MCP"/>
        <xs:element minOccurs="0" ref="TBDOTS"/>
        <xs:element minOccurs="0" ref="ABP"/>
        <xs:element minOccurs="0" ref="NCP"/>
        <xs:element minOccurs="0" ref="HIVAIDS"/>
        <xs:element minOccurs="0" ref="CATARACTINFO"/>
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:element name="PROCEDURES">
    <xs:complexType>
      <xs:sequence>
        <xs:sequence>
          <xs:element minOccurs="0" ref="HEMODIALYSIS"/>
          <xs:element minOccurs="0" ref="PERITONEAL"/>
          <xs:element minOccurs="0" ref="LINAC"/>
          <xs:element minOccurs="0" ref="COBALT"/>
          <xs:element minOccurs="0" ref="TRANSFUSION"/>
          <xs:element minOccurs="0" ref="BRACHYTHERAPHY"/>
          <xs:element minOccurs="0" ref="CHEMOTHERAPY"/>
          <xs:element minOccurs="0" ref="DEBRIDEMENT"/>
          <xs:element minOccurs="0" ref="IMRT"/>
        </xs:sequence>
        <xs:choice>
          <xs:element ref="HEMODIALYSIS"/>
          <xs:element ref="PERITONEAL"/>
          <xs:element ref="LINAC"/>
          <xs:element ref="COBALT"/>
          <xs:element ref="TRANSFUSION"/>
          <xs:element ref="BRACHYTHERAPHY"/>
          <xs:element ref="CHEMOTHERAPY"/>
          <xs:element ref="DEBRIDEMENT"/>
          <xs:element ref="IMRT"/>
        </xs:choice>
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:element name="HEMODIALYSIS">
    <xs:complexType>
      <xs:sequence>
        <xs:element minOccurs="0" maxOccurs="unbounded" ref="SESSIONS"/>
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:element name="PERITONEAL">
    <xs:complexType>
      <xs:sequence>
        <xs:element minOccurs="0" maxOccurs="unbounded" ref="SESSIONS"/>
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:element name="LINAC">
    <xs:complexType>
      <xs:sequence>
        <xs:element minOccurs="0" maxOccurs="unbounded" ref="SESSIONS"/>
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:element name="COBALT">
    <xs:complexType>
      <xs:sequence>
        <xs:element minOccurs="0" maxOccurs="unbounded" ref="SESSIONS"/>
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:element name="TRANSFUSION">
    <xs:complexType>
      <xs:sequence>
        <xs:element minOccurs="0" maxOccurs="unbounded" ref="SESSIONS"/>
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:element name="BRACHYTHERAPHY">
    <xs:complexType>
      <xs:sequence>
        <xs:element minOccurs="0" maxOccurs="unbounded" ref="SESSIONS"/>
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:element name="CHEMOTHERAPY">
    <xs:complexType>
      <xs:sequence>
        <xs:element minOccurs="0" maxOccurs="unbounded" ref="SESSIONS"/>
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:element name="DEBRIDEMENT">
    <xs:complexType>
      <xs:sequence>
        <xs:element minOccurs="0" maxOccurs="unbounded" ref="SESSIONS"/>
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:element name="IMRT">
    <xs:complexType>
      <xs:sequence>
        <xs:element minOccurs="0" maxOccurs="unbounded" ref="SESSIONS"/>
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:element name="SESSIONS">
    <xs:complexType>
      <xs:attribute name="pSessionDate" use="required"/>
    </xs:complexType>
  </xs:element>
  <xs:element name="MCP">
    <xs:complexType>
      <xs:attribute name="pCheckUpDate1" use="required"/>
      <xs:attribute name="pCheckUpDate2" use="required"/>
      <xs:attribute name="pCheckUpDate3" use="required"/>
      <xs:attribute name="pCheckUpDate4" use="required"/>
    </xs:complexType>
  </xs:element>
  <xs:element name="TBDOTS">
    <xs:complexType>
      <xs:attribute name="pTBType" use="required">
        <xs:simpleType>
          <xs:restriction base="xs:token">
            <xs:enumeration value="I"/>
            <xs:enumeration value="M"/>
          </xs:restriction>
        </xs:simpleType>
      </xs:attribute>
      <xs:attribute name="pNTPCardNo" use="required"/>
    </xs:complexType>
  </xs:element>
  <xs:element name="ABP">
    <xs:complexType>
      <xs:attribute name="pDay0ARV" use="required"/>
      <xs:attribute name="pDay3ARV" use="required"/>
      <xs:attribute name="pDay7ARV" use="required"/>
      <xs:attribute name="pRIG" use="required"/>
      <xs:attribute name="pABPOthers" use="required"/>
      <xs:attribute name="pABPSpecify" use="required"/>
    </xs:complexType>
  </xs:element>
  <xs:element name="NCP">
    <xs:complexType>
      <xs:sequence>
        <xs:element minOccurs="0" ref="ESSENTIAL"/>
      </xs:sequence>
      <xs:attribute name="pEssentialNewbornCare" use="required">
        <xs:simpleType>
          <xs:restriction base="xs:token">
            <xs:enumeration value="Y"/>
            <xs:enumeration value="N"/>
          </xs:restriction>
        </xs:simpleType>
      </xs:attribute>
      <xs:attribute name="pNewbornHearingScreeningTest" use="required">
        <xs:simpleType>
          <xs:restriction base="xs:token">
            <xs:enumeration value="Y"/>
            <xs:enumeration value="N"/>
          </xs:restriction>
        </xs:simpleType>
      </xs:attribute>
      <xs:attribute name="pNewbornScreeningTest" use="required">
        <xs:simpleType>
          <xs:restriction base="xs:token">
            <xs:enumeration value="Y"/>
            <xs:enumeration value="N"/>
          </xs:restriction>
        </xs:simpleType>
      </xs:attribute>
      <xs:attribute name="pFilterCardNo" use="required"/>
    </xs:complexType>
  </xs:element>
  <xs:element name="ESSENTIAL">
    <xs:complexType>
      <xs:attribute name="pDrying" use="required">
        <xs:simpleType>
          <xs:restriction base="xs:token">
            <xs:enumeration value="Y"/>
            <xs:enumeration value="N"/>
          </xs:restriction>
        </xs:simpleType>
      </xs:attribute>
      <xs:attribute name="pSkinToSkin" use="required">
        <xs:simpleType>
          <xs:restriction base="xs:token">
            <xs:enumeration value="Y"/>
            <xs:enumeration value="N"/>
          </xs:restriction>
        </xs:simpleType>
      </xs:attribute>
      <xs:attribute name="pCordClamping" use="required">
        <xs:simpleType>
          <xs:restriction base="xs:token">
            <xs:enumeration value="Y"/>
            <xs:enumeration value="N"/>
          </xs:restriction>
        </xs:simpleType>
      </xs:attribute>
      <xs:attribute name="pProphylaxis" use="required">
        <xs:simpleType>
          <xs:restriction base="xs:token">
            <xs:enumeration value="Y"/>
            <xs:enumeration value="N"/>
          </xs:restriction>
        </xs:simpleType>
      </xs:attribute>
      <xs:attribute name="pWeighing" use="required">
        <xs:simpleType>
          <xs:restriction base="xs:token">
            <xs:enumeration value="Y"/>
            <xs:enumeration value="N"/>
          </xs:restriction>
        </xs:simpleType>
      </xs:attribute>
      <xs:attribute name="pVitaminK" use="required">
        <xs:simpleType>
          <xs:restriction base="xs:token">
            <xs:enumeration value="Y"/>
            <xs:enumeration value="N"/>
          </xs:restriction>
        </xs:simpleType>
      </xs:attribute>
      <xs:attribute name="pBCG" use="required">
        <xs:simpleType>
          <xs:restriction base="xs:token">
            <xs:enumeration value="Y"/>
            <xs:enumeration value="N"/>
          </xs:restriction>
        </xs:simpleType>
      </xs:attribute>
      <xs:attribute name="pNonSeparation" use="required">
        <xs:simpleType>
          <xs:restriction base="xs:token">
            <xs:enumeration value="Y"/>
            <xs:enumeration value="N"/>
          </xs:restriction>
        </xs:simpleType>
      </xs:attribute>
      <xs:attribute name="pHepatitisB" use="required">
        <xs:simpleType>
          <xs:restriction base="xs:token">
            <xs:enumeration value="Y"/>
            <xs:enumeration value="N"/>
          </xs:restriction>
        </xs:simpleType>
      </xs:attribute>
    </xs:complexType>
  </xs:element>
  <xs:element name="HIVAIDS">
    <xs:complexType>
      <xs:attribute name="pLaboratoryNumber" use="required"/>
    </xs:complexType>
  </xs:element>
  <xs:element name="CATARACTINFO">
    <xs:complexType>
      <xs:attribute name="pCataractPreAuth" use="required"/>
      <xs:attribute name="pLeftEyeIOLStickerNumber" use="required"/>
      <xs:attribute name="pLeftEyeIOLExpiryDate" use="required"/>
      <xs:attribute name="pRightEyeIOLStickerNumber" use="required"/>
      <xs:attribute name="pRightEyeIOLExpiryDate" use="required"/>
    </xs:complexType>
  </xs:element>
  <xs:element name="PROFESSIONALS">
    <xs:complexType>
      <xs:attribute name="pDoctorAccreCode" use="required"/>
      <xs:attribute name="pDoctorLastName" use="required"/>
      <xs:attribute name="pDoctorFirstName" use="required"/>
      <xs:attribute name="pDoctorMiddleName" use="required"/>
      <xs:attribute name="pDoctorSuffix" use="required"/>
      <xs:attribute name="pWithCoPay" use="required">
        <xs:simpleType>
          <xs:restriction base="xs:token">
            <xs:enumeration value="Y"/>
            <xs:enumeration value="N"/>
          </xs:restriction>
        </xs:simpleType>
      </xs:attribute>
      <xs:attribute name="pDoctorCoPay" use="required"/>
      <xs:attribute name="pDoctorSignDate" use="required"/>
    </xs:complexType>
  </xs:element>
  <xs:element name="CONSUMPTION">
    <xs:complexType>
      <xs:choice>
        <xs:element ref="BENEFITS"/>
        <xs:sequence>
          <xs:element ref="HCIFEES"/>
          <xs:element ref="PROFFEES"/>
          <xs:element ref="PURCHASES"/>
        </xs:sequence>
      </xs:choice>
      <xs:attribute name="pEnoughBenefits" use="required">
        <xs:simpleType>
          <xs:restriction base="xs:token">
            <xs:enumeration value="Y"/>
            <xs:enumeration value="N"/>
          </xs:restriction>
        </xs:simpleType>
      </xs:attribute>
    </xs:complexType>
  </xs:element>
  <xs:element name="APR">
    <xs:complexType>
      <xs:choice>
        <xs:element ref="APRBYPATSIG"/>
        <xs:element ref="APRBYPATREPSIG"/>
        <xs:element ref="APRBYTHUMBMARK"/>
      </xs:choice>
    </xs:complexType>
  </xs:element>
  <xs:element name="APRBYPATSIG">
    <xs:complexType>
      <xs:attribute name="pDateSigned" use="required"/>
    </xs:complexType>
  </xs:element>
  <xs:element name="APRBYPATREPSIG">
    <xs:complexType>
      <xs:sequence>
        <xs:choice>
          <xs:element ref="DEFINEDPATREPREL"/>
          <xs:element ref="OTHERPATREPREL"/>
        </xs:choice>
        <xs:choice>
          <xs:element ref="DEFINEDREASONFORSIGNING"/>
          <xs:element ref="OTHERREASONFORSIGNING"/>
        </xs:choice>
      </xs:sequence>
      <xs:attribute name="pDateSigned" use="required"/>
    </xs:complexType>
  </xs:element>
  <xs:element name="DEFINEDPATREPREL">
    <xs:complexType>
      <xs:attribute name="pRelCode" use="required">
        <xs:simpleType>
          <xs:restriction base="xs:token">
            <xs:enumeration value="S"/>
            <xs:enumeration value="C"/>
            <xs:enumeration value="P"/>
            <xs:enumeration value="I"/>
          </xs:restriction>
        </xs:simpleType>
      </xs:attribute>
    </xs:complexType>
  </xs:element>
  <!--
    pRelCode:                  One of
    S: Spouse
    C: Child
    P: Parent
    I: Siblings
    O: Others
  -->
  <xs:element name="OTHERPATREPREL">
    <xs:complexType>
      <xs:attribute name="pRelCode" default="O">
        <xs:simpleType>
          <xs:restriction base="xs:string">
            <xs:enumeration value="O"/>
          </xs:restriction>
        </xs:simpleType>
      </xs:attribute>
      <xs:attribute name="pRelDesc" use="required"/>
    </xs:complexType>
  </xs:element>
  <xs:element name="DEFINEDREASONFORSIGNING">
    <xs:complexType>
      <xs:attribute name="pReasonCode" use="required">
        <xs:simpleType>
          <xs:restriction base="xs:token">
            <xs:enumeration value="I"/>
          </xs:restriction>
        </xs:simpleType>
      </xs:attribute>
    </xs:complexType>
  </xs:element>
  <!--
    pReasonCode:    One of
    I: Patient is incapacitated
    O: Other reasons. Should be specified in pReasonDesc
  -->
  <xs:element name="OTHERREASONFORSIGNING">
    <xs:complexType>
      <xs:attribute name="pReasonCode" default="O">
        <xs:simpleType>
          <xs:restriction base="xs:string">
            <xs:enumeration value="O"/>
          </xs:restriction>
        </xs:simpleType>
      </xs:attribute>
      <xs:attribute name="pReasonDesc" use="required"/>
    </xs:complexType>
  </xs:element>
  <xs:element name="APRBYTHUMBMARK">
    <xs:complexType>
      <xs:attribute name="pThumbmarkedBy" use="required">
        <xs:simpleType>
          <xs:restriction base="xs:token">
            <xs:enumeration value="P"/>
            <xs:enumeration value="R"/>
          </xs:restriction>
        </xs:simpleType>
      </xs:attribute>
    </xs:complexType>
  </xs:element>
  <!--
    pThumbmarkedBy:    One of
    P: by patient/member
    R: by representative
  -->
  <xs:element name="BENEFITS">
    <xs:complexType>
      <xs:attribute name="pTotalHCIFees" use="required"/>
      <xs:attribute name="pTotalProfFees" use="required"/>
      <xs:attribute name="pGrandTotal" use="required"/>
    </xs:complexType>
  </xs:element>
  <xs:element name="HCIFEES">
    <xs:complexType>
      <xs:attribute name="pTotalActualCharges" use="required"/>
      <xs:attribute name="pDiscount" use="required"/>
      <xs:attribute name="pPhilhealthBenefit" use="required"/>
      <xs:attribute name="pTotalAmount" use="required"/>
      <xs:attribute name="pMemberPatient" use="required">
        <xs:simpleType>
          <xs:restriction base="xs:token">
            <xs:enumeration value="Y"/>
            <xs:enumeration value="N"/>
          </xs:restriction>
        </xs:simpleType>
      </xs:attribute>
      <xs:attribute name="pHMO" use="required">
        <xs:simpleType>
          <xs:restriction base="xs:token">
            <xs:enumeration value="Y"/>
            <xs:enumeration value="N"/>
          </xs:restriction>
        </xs:simpleType>
      </xs:attribute>
      <xs:attribute name="pOthers" use="required">
        <xs:simpleType>
          <xs:restriction base="xs:token">
            <xs:enumeration value="Y"/>
            <xs:enumeration value="N"/>
          </xs:restriction>
        </xs:simpleType>
      </xs:attribute>
    </xs:complexType>
  </xs:element>
  <xs:element name="PROFFEES">
    <xs:complexType>
      <xs:attribute name="pTotalActualCharges" use="required"/>
      <xs:attribute name="pDiscount" use="required"/>
      <xs:attribute name="pPhilhealthBenefit" use="required"/>
      <xs:attribute name="pTotalAmount" use="required"/>
      <xs:attribute name="pMemberPatient" use="required">
        <xs:simpleType>
          <xs:restriction base="xs:token">
            <xs:enumeration value="Y"/>
            <xs:enumeration value="N"/>
          </xs:restriction>
        </xs:simpleType>
      </xs:attribute>
      <xs:attribute name="pHMO" use="required">
        <xs:simpleType>
          <xs:restriction base="xs:token">
            <xs:enumeration value="Y"/>
            <xs:enumeration value="N"/>
          </xs:restriction>
        </xs:simpleType>
      </xs:attribute>
      <xs:attribute name="pOthers" use="required">
        <xs:simpleType>
          <xs:restriction base="xs:token">
            <xs:enumeration value="Y"/>
            <xs:enumeration value="N"/>
          </xs:restriction>
        </xs:simpleType>
      </xs:attribute>
    </xs:complexType>
  </xs:element>
  <xs:element name="PURCHASES">
    <xs:complexType>
      <xs:attribute name="pDrugsMedicinesSupplies" use="required">
        <xs:simpleType>
          <xs:restriction base="xs:token">
            <xs:enumeration value="Y"/>
            <xs:enumeration value="N"/>
          </xs:restriction>
        </xs:simpleType>
      </xs:attribute>
      <xs:attribute name="pDMSTotalAmount" use="required"/>
      <xs:attribute name="pExaminations" use="required">
        <xs:simpleType>
          <xs:restriction base="xs:token">
            <xs:enumeration value="Y"/>
            <xs:enumeration value="N"/>
          </xs:restriction>
        </xs:simpleType>
      </xs:attribute>
      <xs:attribute name="pExamTotalAmount" use="required"/>
    </xs:complexType>
  </xs:element>
  <xs:element name="ALLCASERATE">
    <xs:complexType>
      <xs:sequence>
        <xs:element maxOccurs="unbounded" ref="CASERATE"/>
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:element name="CASERATE">
    <xs:complexType>
      <xs:sequence>
        <xs:element minOccurs="0" ref="CATARACT"/>
      </xs:sequence>
      <xs:attribute name="pCaseRateCode" use="required"/>
      <xs:attribute name="pICDCode" use="required"/>
      <xs:attribute name="pRVSCode" use="required"/>
      <xs:attribute name="pCaseRateAmount" use="required"/>
    </xs:complexType>
  </xs:element>
  <xs:element name="CATARACT">
    <xs:complexType>
      <xs:attribute name="pCataractPreAuth" use="required"/>
    </xs:complexType>
  </xs:element>
  <xs:element name="ZBENEFIT">
    <xs:complexType>
      <xs:attribute name="pZBenefitCode" use="required">
        <xs:simpleType>
          <xs:restriction base="xs:token">
            <xs:enumeration value="Z0011"/>
            <xs:enumeration value="Z0012"/>
            <xs:enumeration value="Z0013"/>
            <xs:enumeration value="Z0021"/>
            <xs:enumeration value="Z0022"/>
            <xs:enumeration value="Z003"/>
            <xs:enumeration value="Z0041"/>
            <xs:enumeration value="Z0042"/>
            <xs:enumeration value="Z0051"/>
            <xs:enumeration value="Z0052"/>
            <xs:enumeration value="Z0061"/>
            <xs:enumeration value="Z0062"/>
            <xs:enumeration value="Z0071"/>
            <xs:enumeration value="Z0072"/>
            <xs:enumeration value="Z0081"/>
            <xs:enumeration value="Z0082"/>
            <xs:enumeration value="Z0091"/>
            <xs:enumeration value="Z0092"/>
          </xs:restriction>
        </xs:simpleType>
      </xs:attribute>
      <xs:attribute name="pPreAuthDate" use="required"/>
    </xs:complexType>
  </xs:element>
  <xs:element name="CF3">
    <xs:complexType>
      <xs:sequence>
        <xs:element minOccurs="0" ref="CF3_OLD"/>
        <xs:element minOccurs="0" ref="CF3_NEW"/>
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:element name="CF3_OLD">
    <xs:complexType>
      <xs:sequence>
        <xs:element ref="PHEX"/>
        <xs:element minOccurs="0" ref="MATERNITY"/>
      </xs:sequence>
      <xs:attribute name="pChiefComplaint" use="required"/>
      <xs:attribute name="pBriefHistory" use="required"/>
      <xs:attribute name="pCourseWard" use="required"/>
      <xs:attribute name="pPertinentFindings" use="required"/>
    </xs:complexType>
  </xs:element>
  <xs:element name="MATERNITY">
    <xs:complexType>
      <xs:sequence>
        <xs:element ref="PRENATAL"/>
        <xs:element ref="DELIVERY"/>
        <xs:element ref="POSTPARTUM"/>
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:element name="PRENATAL">
    <xs:complexType>
      <xs:sequence>
        <xs:element ref="CLINICALHIST"/>
        <xs:element ref="OBSTETRIC"/>
        <xs:element ref="MEDISURG"/>
        <xs:element maxOccurs="unbounded" ref="CONSULTATION"/>
      </xs:sequence>
      <xs:attribute name="pPrenatalConsultation" use="required"/>
      <xs:attribute name="pMCPOrientation" use="required">
        <xs:simpleType>
          <xs:restriction base="xs:token">
            <xs:enumeration value="Y"/>
            <xs:enumeration value="N"/>
          </xs:restriction>
        </xs:simpleType>
      </xs:attribute>
      <xs:attribute name="pExpectedDeliveryDate" use="required"/>
    </xs:complexType>
  </xs:element>
  <xs:element name="CLINICALHIST">
    <xs:complexType>
      <xs:attribute name="pVitalSigns" use="required">
        <xs:simpleType>
          <xs:restriction base="xs:token">
            <xs:enumeration value="Y"/>
            <xs:enumeration value="N"/>
          </xs:restriction>
        </xs:simpleType>
      </xs:attribute>
      <xs:attribute name="pPregnancyLowRisk" use="required">
        <xs:simpleType>
          <xs:restriction base="xs:token">
            <xs:enumeration value="Y"/>
            <xs:enumeration value="N"/>
          </xs:restriction>
        </xs:simpleType>
      </xs:attribute>
      <xs:attribute name="pLMP" use="required"/>
      <xs:attribute name="pMenarcheAge" use="required"/>
      <xs:attribute name="pObstetricG" use="required"/>
      <xs:attribute name="pObstetricP" use="required"/>
      <xs:attribute name="pObstetric_T" use="required"/>
      <xs:attribute name="pObstetric_P" use="required"/>
      <xs:attribute name="pObstetric_A" use="required"/>
      <xs:attribute name="pObstetric_L" use="required"/>
    </xs:complexType>
  </xs:element>
  <xs:element name="OBSTETRIC">
    <xs:complexType>
      <xs:attribute name="pMultiplePregnancy" use="required">
        <xs:simpleType>
          <xs:restriction base="xs:token">
            <xs:enumeration value="Y"/>
            <xs:enumeration value="N"/>
          </xs:restriction>
        </xs:simpleType>
      </xs:attribute>
      <xs:attribute name="pOvarianCyst" use="required">
        <xs:simpleType>
          <xs:restriction base="xs:token">
            <xs:enumeration value="Y"/>
            <xs:enumeration value="N"/>
          </xs:restriction>
        </xs:simpleType>
      </xs:attribute>
      <xs:attribute name="pMyomaUteri" use="required">
        <xs:simpleType>
          <xs:restriction base="xs:token">
            <xs:enumeration value="Y"/>
            <xs:enumeration value="N"/>
          </xs:restriction>
        </xs:simpleType>
      </xs:attribute>
      <xs:attribute name="pPlacentaPrevia" use="required">
        <xs:simpleType>
          <xs:restriction base="xs:token">
            <xs:enumeration value="Y"/>
            <xs:enumeration value="N"/>
          </xs:restriction>
        </xs:simpleType>
      </xs:attribute>
      <xs:attribute name="pMiscarriages" use="required">
        <xs:simpleType>
          <xs:restriction base="xs:token">
            <xs:enumeration value="Y"/>
            <xs:enumeration value="N"/>
          </xs:restriction>
        </xs:simpleType>
      </xs:attribute>
      <xs:attribute name="pStillBirth" use="required">
        <xs:simpleType>
          <xs:restriction base="xs:token">
            <xs:enumeration value="Y"/>
            <xs:enumeration value="N"/>
          </xs:restriction>
        </xs:simpleType>
      </xs:attribute>
      <xs:attribute name="pPreEclampsia" use="required">
        <xs:simpleType>
          <xs:restriction base="xs:token">
            <xs:enumeration value="Y"/>
            <xs:enumeration value="N"/>
          </xs:restriction>
        </xs:simpleType>
      </xs:attribute>
      <xs:attribute name="pEclampsia" use="required">
        <xs:simpleType>
          <xs:restriction base="xs:token">
            <xs:enumeration value="Y"/>
            <xs:enumeration value="N"/>
          </xs:restriction>
        </xs:simpleType>
      </xs:attribute>
      <xs:attribute name="pPrematureContraction" use="required">
        <xs:simpleType>
          <xs:restriction base="xs:token">
            <xs:enumeration value="Y"/>
            <xs:enumeration value="N"/>
          </xs:restriction>
        </xs:simpleType>
      </xs:attribute>
    </xs:complexType>
  </xs:element>
  <xs:element name="MEDISURG">
    <xs:complexType>
      <xs:attribute name="pHypertension" use="required">
        <xs:simpleType>
          <xs:restriction base="xs:token">
            <xs:enumeration value="Y"/>
            <xs:enumeration value="N"/>
          </xs:restriction>
        </xs:simpleType>
      </xs:attribute>
      <xs:attribute name="pHeartDisease" use="required">
        <xs:simpleType>
          <xs:restriction base="xs:token">
            <xs:enumeration value="Y"/>
            <xs:enumeration value="N"/>
          </xs:restriction>
        </xs:simpleType>
      </xs:attribute>
      <xs:attribute name="pDiabetes" use="required">
        <xs:simpleType>
          <xs:restriction base="xs:token">
            <xs:enumeration value="Y"/>
            <xs:enumeration value="N"/>
          </xs:restriction>
        </xs:simpleType>
      </xs:attribute>
      <xs:attribute name="pThyroidDisaster" use="required">
        <xs:simpleType>
          <xs:restriction base="xs:token">
            <xs:enumeration value="Y"/>
            <xs:enumeration value="N"/>
          </xs:restriction>
        </xs:simpleType>
      </xs:attribute>
      <xs:attribute name="pObesity" use="required">
        <xs:simpleType>
          <xs:restriction base="xs:token">
            <xs:enumeration value="Y"/>
            <xs:enumeration value="N"/>
          </xs:restriction>
        </xs:simpleType>
      </xs:attribute>
      <xs:attribute name="pAsthma" use="required">
        <xs:simpleType>
          <xs:restriction base="xs:token">
            <xs:enumeration value="Y"/>
            <xs:enumeration value="N"/>
          </xs:restriction>
        </xs:simpleType>
      </xs:attribute>
      <xs:attribute name="pEpilepsy" use="required">
        <xs:simpleType>
          <xs:restriction base="xs:token">
            <xs:enumeration value="Y"/>
            <xs:enumeration value="N"/>
          </xs:restriction>
        </xs:simpleType>
      </xs:attribute>
      <xs:attribute name="pRenalDisease" use="required">
        <xs:simpleType>
          <xs:restriction base="xs:token">
            <xs:enumeration value="Y"/>
            <xs:enumeration value="N"/>
          </xs:restriction>
        </xs:simpleType>
      </xs:attribute>
      <xs:attribute name="pBleedingDisorders" use="required">
        <xs:simpleType>
          <xs:restriction base="xs:token">
            <xs:enumeration value="Y"/>
            <xs:enumeration value="N"/>
          </xs:restriction>
        </xs:simpleType>
      </xs:attribute>
      <xs:attribute name="pPreviousCS" use="required">
        <xs:simpleType>
          <xs:restriction base="xs:token">
            <xs:enumeration value="Y"/>
            <xs:enumeration value="N"/>
          </xs:restriction>
        </xs:simpleType>
      </xs:attribute>
      <xs:attribute name="pUterineMyomectomy" use="required">
        <xs:simpleType>
          <xs:restriction base="xs:token">
            <xs:enumeration value="Y"/>
            <xs:enumeration value="N"/>
          </xs:restriction>
        </xs:simpleType>
      </xs:attribute>
    </xs:complexType>
  </xs:element>
  <xs:element name="CONSULTATION">
    <xs:complexType>
      <xs:attribute name="pVisitDate" use="required"/>
      <xs:attribute name="pAOGWeeks" use="required"/>
      <xs:attribute name="pWeight" use="required"/>
      <xs:attribute name="pCardiacRate" use="required"/>
      <xs:attribute name="pRespiratoryRate" use="required"/>
      <xs:attribute name="pBloodPressure" use="required"/>
      <xs:attribute name="pTemperature" use="required"/>
    </xs:complexType>
  </xs:element>
  <xs:element name="DELIVERY">
    <xs:complexType>
      <xs:attribute name="pDeliveryDate" use="required"/>
      <xs:attribute name="pDeliveryTime" use="required"/>
      <xs:attribute name="pObstetricIndex" use="required"/>
      <xs:attribute name="pAOGLMP" use="required"/>
      <xs:attribute name="pDeliveryManner" use="required"/>
      <xs:attribute name="pPresentation" use="required"/>
      <xs:attribute name="pFetalOutcome" use="required"/>
      <xs:attribute name="pSex" use="required"/>
      <xs:attribute name="pBirthWeight" use="required"/>
      <xs:attribute name="pAPGARScore" use="required"/>
      <xs:attribute name="pPostpartum" use="required"/>
    </xs:complexType>
  </xs:element>
  <xs:element name="POSTPARTUM">
    <xs:complexType>
      <xs:attribute name="pPerinealWoundCare" use="required">
        <xs:simpleType>
          <xs:restriction base="xs:token">
            <xs:enumeration value="Y"/>
            <xs:enumeration value="N"/>
          </xs:restriction>
        </xs:simpleType>
      </xs:attribute>
      <xs:attribute name="pPerinealRemarks" use="required"/>
      <xs:attribute name="pMaternalComplications" use="required">
        <xs:simpleType>
          <xs:restriction base="xs:token">
            <xs:enumeration value="Y"/>
            <xs:enumeration value="N"/>
          </xs:restriction>
        </xs:simpleType>
      </xs:attribute>
      <xs:attribute name="pMaternalRemarks" use="required"/>
      <xs:attribute name="pBreastFeeding" use="required">
        <xs:simpleType>
          <xs:restriction base="xs:token">
            <xs:enumeration value="Y"/>
            <xs:enumeration value="N"/>
          </xs:restriction>
        </xs:simpleType>
      </xs:attribute>
      <xs:attribute name="pBreastFeedingRemarks" use="required"/>
      <xs:attribute name="pFamilyPlanning" use="required">
        <xs:simpleType>
          <xs:restriction base="xs:token">
            <xs:enumeration value="Y"/>
            <xs:enumeration value="N"/>
          </xs:restriction>
        </xs:simpleType>
      </xs:attribute>
      <xs:attribute name="pFamilyPlanningRemarks" use="required"/>
      <xs:attribute name="pPlanningService" use="required">
        <xs:simpleType>
          <xs:restriction base="xs:token">
            <xs:enumeration value="Y"/>
            <xs:enumeration value="N"/>
          </xs:restriction>
        </xs:simpleType>
      </xs:attribute>
      <xs:attribute name="pPlanningServiceRemarks" use="required"/>
      <xs:attribute name="pSurgicalSterilization" use="required">
        <xs:simpleType>
          <xs:restriction base="xs:token">
            <xs:enumeration value="Y"/>
            <xs:enumeration value="N"/>
          </xs:restriction>
        </xs:simpleType>
      </xs:attribute>
      <xs:attribute name="pSterilizationRemarks" use="required"/>
      <xs:attribute name="pFollowupSchedule" use="required">
        <xs:simpleType>
          <xs:restriction base="xs:token">
            <xs:enumeration value="Y"/>
            <xs:enumeration value="N"/>
          </xs:restriction>
        </xs:simpleType>
      </xs:attribute>
      <xs:attribute name="pFollowupScheduleRemarks" use="required"/>
    </xs:complexType>
  </xs:element>
  <xs:element name="CF3_NEW">
    <xs:complexType>
      <xs:sequence>
        <xs:element minOccurs="0" ref="ADMITREASON"/>
        <xs:element minOccurs="0" ref="COURSE"/>
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:element name="ADMITREASON">
    <xs:complexType>
      <xs:sequence>
        <xs:element maxOccurs="unbounded" ref="CLINICAL"/>
        <xs:element maxOccurs="unbounded" ref="LABDIAG"/>
        <xs:element ref="PHEX"/>
      </xs:sequence>
      <xs:attribute name="pBriefHistory" use="required"/>
      <xs:attribute name="pReferredReason" use="required"/>
      <xs:attribute name="pIntensive" use="required">
        <xs:simpleType>
          <xs:restriction base="xs:token">
            <xs:enumeration value="Y"/>
            <xs:enumeration value="N"/>
          </xs:restriction>
        </xs:simpleType>
      </xs:attribute>
      <xs:attribute name="pMaintenance" use="required">
        <xs:simpleType>
          <xs:restriction base="xs:token">
            <xs:enumeration value="Y"/>
            <xs:enumeration value="N"/>
          </xs:restriction>
        </xs:simpleType>
      </xs:attribute>
    </xs:complexType>
  </xs:element>
  <xs:element name="CLINICAL">
    <xs:complexType>
      <xs:attribute name="pCriteria" use="required"/>
    </xs:complexType>
  </xs:element>
  <xs:element name="LABDIAG">
    <xs:complexType>
      <xs:attribute name="pCriteria" use="required"/>
    </xs:complexType>
  </xs:element>
  <xs:element name="PHEX">
    <xs:complexType>
      <xs:attribute name="pBP" use="required"/>
      <xs:attribute name="pCR" use="required"/>
      <xs:attribute name="pRR" use="required"/>
      <xs:attribute name="pTemp" use="required"/>
      <xs:attribute name="pHEENT" use="required"/>
      <xs:attribute name="pChestLungs" use="required"/>
      <xs:attribute name="pCVS" use="required"/>
      <xs:attribute name="pAbdomen" use="required"/>
      <xs:attribute name="pGUIE" use="required"/>
      <xs:attribute name="pSkinExtremities" use="required"/>
      <xs:attribute name="pNeuroExam" use="required"/>
    </xs:complexType>
  </xs:element>
  <xs:element name="COURSE">
    <xs:complexType>
      <xs:sequence>
        <xs:element maxOccurs="unbounded" ref="WARD"/>
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:element name="WARD">
    <xs:complexType>
      <xs:attribute name="pCourseDate" use="required"/>
      <xs:attribute name="pFindings" use="required"/>
      <xs:attribute name="pAction" use="required"/>
    </xs:complexType>
  </xs:element>
  <xs:element name="PARTICULARS">
    <xs:complexType>
      <xs:sequence>
        <xs:choice>
          <xs:element maxOccurs="unbounded" ref="DRGMED"/>
          <xs:element maxOccurs="unbounded" ref="XLSO"/>
        </xs:choice>
        <xs:choice>
          <xs:element minOccurs="0" maxOccurs="unbounded" ref="DRGMED"/>
          <xs:element minOccurs="0" maxOccurs="unbounded" ref="XLSO"/>
        </xs:choice>
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:element name="DRGMED">
    <xs:complexType>
      <xs:attribute name="pPurchaseDate" use="required"/>
      <xs:attribute name="pDrugCode" use="required"/>
      <xs:attribute name="pPNDFCode" use="required"/>
      <xs:attribute name="pGenericName" use="required"/>
      <xs:attribute name="pBrandName" use="required"/>
      <xs:attribute name="pPreparation" use="required"/>
      <xs:attribute name="pQuantity" use="required"/>
    </xs:complexType>
  </xs:element>
  <xs:element name="XLSO">
    <xs:complexType>
      <xs:attribute name="pDiagnosticDate" use="required"/>
      <xs:attribute name="pDiagnosticType" use="required">
        <xs:simpleType>
          <xs:restriction base="xs:token">
            <xs:enumeration value="IMAGING"/>
            <xs:enumeration value="LABORATORY"/>
            <xs:enumeration value="SUPPLIES"/>
            <xs:enumeration value="OTHERS"/>
          </xs:restriction>
        </xs:simpleType>
      </xs:attribute>
      <xs:attribute name="pDiagnosticName" use="required"/>
      <xs:attribute name="pQuantity" use="required"/>
    </xs:complexType>
  </xs:element>
  <xs:element name="RECEIPTS">
    <xs:complexType>
      <xs:sequence>
        <xs:element maxOccurs="unbounded" ref="RECEIPT"/>
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:element name="RECEIPT">
    <xs:complexType>
      <xs:sequence>
        <xs:element maxOccurs="unbounded" ref="ITEM"/>
      </xs:sequence>
      <xs:attribute name="pCompanyName" use="required"/>
      <xs:attribute name="pCompanyTIN" use="required"/>
      <xs:attribute name="pBIRPermitNumber" use="required"/>
      <xs:attribute name="pReceiptNumber" use="required"/>
      <xs:attribute name="pReceiptDate" use="required"/>
      <xs:attribute name="pVATExemptSale" use="required"/>
      <xs:attribute name="pVAT" use="required"/>
      <xs:attribute name="pTotal" use="required"/>
    </xs:complexType>
  </xs:element>
  <xs:element name="ITEM">
    <xs:complexType>
      <xs:attribute name="pQuantity" use="required"/>
      <xs:attribute name="pUnitPrice" use="required"/>
      <xs:attribute name="pDescription" use="required"/>
      <xs:attribute name="pAmount" use="required"/>
    </xs:complexType>
  </xs:element>
  <xs:element name="DOCUMENTS">
    <xs:complexType>
      <xs:sequence>
        <xs:element maxOccurs="unbounded" ref="DOCUMENT"/>
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:element name="DOCUMENT">
    <xs:complexType>
      <xs:attribute name="pDocumentType" use="required"/>
      <xs:attribute name="pDocumentURL" use="required"/>
    </xs:complexType>
  </xs:element>
</xs:schema>
