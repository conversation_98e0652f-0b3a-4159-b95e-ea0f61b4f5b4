<?php

namespace App\Http\Controllers;

use App\Services\ESOAGenerationService;
use Illuminate\Http\Request;

class ESOAGenerationController extends Controller
{
    //use App\Services\XmlGenerationService;

    public function generateESOAStatement(ESOAGenerationService $xmlService)
    {
        $data = [
            'SummaryOfFees' => [
                'RoomAndBoard' => '5000',
                'DrugsAndMedicine' => '2000',
                'LaboratoryAndDiagnostic' => '1000',
                // Add other fields as needed
            ],
            'ProfessionalFees' => [
                'ProfessionalFee' => ['1500', '2000'],
                'PhilHealth' => '3000',
                'Balance' => '200',
            ],
            'ItemizedBillingItems' => [
                'BillingItem' => [
                    ['Description' => 'X-ray', 'Amount' => '500'],
                    ['Description' => 'Blood Test', 'Amount' => '250'],
                ],
            ],
        ];

        return response($xmlService->generateXml($data), 200, [
            'Content-Type' => 'application/xml',
        ]);
    }
}
