<?php

namespace App\Http\Controllers;

use App\Services\PECWS3Service;
use App\Services\XMLServices\eClaimsXMLService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

class eClaimsXMLController extends Controller
{
    //
    protected $eClaimsXMLService;
    protected $pecws3Service;

    public function __construct(
        eClaimsXMLService $eClaimsXMLService,
        PECWS3Service $pecws3Service
    ) {
        $this->eClaimsXMLService = $eClaimsXMLService;
        $this->pecws3Service = $pecws3Service;
    }

    public function generate(Request $request)
    {
        // Decode JSON from the request
        // $enccode = json_decode($request->getContent(), true);
        $enccode = (string) $request->enccode;
        // return $enccode;
        return $this->eClaimsXMLService->generate($enccode);
    }
    public function retrieve(Request $request)
    {
        // Decode JSON from the request
        // $enccode = json_decode($request->getContent(), true);
        $enccode = (string) $request->enccode;
        // return $enccode;
        return $this->eClaimsXMLService->retrieve($enccode);
    }

    public function validate_local(Request $request)
    {
        $xml = $request->getContent();
        return $this->eClaimsXMLService->validate_local($xml);
    }

    public function validate_remote(Request $request)
    {
        // $xml = $request->getContent();
        // return $xml;
        // $xml = $request;
        return $this->eClaimsXMLService->validate_remote($request);
    }

    public function eClaimsFileCheck(Request $request)
    {
        $enccode = $request['enccode'];
        // $xml = $request->getContent();
        return $this->eClaimsXMLService->eClaimsFileCheck($enccode);
    }

    public function uploadeClaims(Request $request)
    {
        $enccode = $request['enccode'];
        // $xml = $request->getContent();
        return $this->eClaimsXMLService->uploadeClaims($enccode);
    }

    public function isClaimEligible(Request $request)
    {
        $enccode = $request['enccode'];
        // $xml = $request->getContent();
        return $this->eClaimsXMLService->isClaimEligible($enccode);
    }

    public function eClaimsPBEFPDF(Request $request)
    {
        $enccode = $request['pdfPath'];
        // $xml = $request->getContent();
        return $this->eClaimsXMLService->eClaimsPBEFPDF($enccode);
    }
}
