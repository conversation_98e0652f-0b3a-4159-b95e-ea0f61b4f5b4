<?php

namespace Tests\Unit;

use App\Repositories\EClaimsRepository;
use App\Services\EClaimsTokenService;
use App\Services\ExternalAPIService;
use App\Services\PhilhealthEncryptionService;
use App\Services\Validators\eClaimsValidatorService;
use App\Services\XMLServices\eClaimsXMLService;
use Tests\TestCase;
use PHPUnit\Framework\Attributes\Test;

// use PHPUnit\Framework\TestCase;


class eClaimsXMLServiceTest extends TestCase
{
    protected $eClaimsRepositoryMock;
    protected $eClaimsValidatorMock;
    protected $encryptionServiceMock;

    protected $externalAPIServiceMock;

    protected $eClaimsTokenServiceMock;
    protected $service;

    protected function setUp(): void
    {
        parent::setUp();

        // Create mocks for each of the dependencies using PHPUnit's createMock
        $this->eClaimsRepositoryMock = $this->createMock(EClaimsRepository::class);
        $this->eClaimsValidatorMock = $this->createMock(eClaimsValidatorService::class);
        $this->encryptionServiceMock = $this->createMock(PhilhealthEncryptionService::class);
        $this->externalAPIServiceMock = $this->createMock(ExternalAPIService::class);
        $this->eClaimsTokenServiceMock = $this->createMock(eClaimsTokenService::class);

        // Instantiate the service with the mocked dependencies
        $this->service = new eClaimsXMLService(
            $this->eClaimsRepositoryMock,
            $this->eClaimsValidatorMock,
            $this->encryptionServiceMock,
            $this->externalAPIServiceMock
        );
    }

    #[Test]
    public function generates_eclaims_xml_using_the_repository(): void
    {
        // Arrange
        $enccode = '{ "enccode" : "ADM1000394Jan062025160825"}';
        $expectedData = '<eCLAIMS pUserName=":DUMMYSCERT20101" ...> ... </eCLAIMS>';

        // Tell the repository mock what to expect
        $this->eClaimsRepositoryMock
            ->expects($this->once())         // We expect exactly one call
            ->method('generateeClaimsData')       // to the 'geteClaimsData' method
            ->with($enccode)                // with $enccode as the argument
            ->willReturn($expectedData);    // and it should return $expectedData
        // ->willReturn('...');    // and it should return $expectedData

        // Act
        $result = $this->service->generate($enccode);

        // Assert
        $this->assertEquals($expectedData, $result);
    }

    // /** @test */
    #[Test]
    public function validates_eclaims_xml_using_the_validator(): void
    {
        // Arrange
        $xml = '<xml>some xml content</xml>';
        $expectedResult = [
            'message' => 'eClaims XML is valid against DTD!',
        ];

        $this->eClaimsValidatorMock
            ->expects($this->once())
            ->method('validateXML')
            ->with($xml)
            ->willReturn($expectedResult);

        // Act
        $result = $this->service->validate_local($xml);

        // Assert
        $this->assertEquals($expectedResult, $result);
    }

    // /** @test */
    #[Test]
    public function encrypts_data_using_the_encryption_service(): void
    {
        // Arrange
        $xmlData = '<xml>secret data</xml>';
        $encryptedData = '{
    "docMimeType": "text/xml",
    "hash": "6c55310c98706f59b9fd8237941b3712e76b2d6caddfe1be63b1c7e54aa1ea95",
    "key1": "",
    "key2": "",
    "iv": "n67yis3+NweEtXBcewx1rg==",
    "doc": "4HgV1sKW+zefUuE2keNXpWbEyyXhDXcaMvVCKgl4beI="
}';

        $this->encryptionServiceMock
            ->expects($this->once())
            ->method('encryptMessage')
            ->with($xmlData)
            ->willReturn($encryptedData);

        // Act
        $result = $this->service->encrypt($xmlData);

        // Assert
        $this->assertEquals($encryptedData, $result);
    }

    // /** @test */
    #[Test]
    public function sends_encrypted_data_to_pecws3_and_receives_response(): void
    {
        // Arrange
        $payload = '{
                        "docMimeType": "text/xml",
                        "hash": "d0a36b6bfe4e531b5500c3c1eebafe01f7997c85389362c9e76fe6a6d0af0650",
                        "key1": "",
                        "key2": "",
                        "iv": "UfHSa3QFt/COM2eCBeZRig==",
                        "doc": "zpm3gnV4qYukhFp0="
                    }';
        $expected = ['success' => true];
        $url = 'https://ecstest.philhealth.gov.ph/PHIC/Claims3.0/eClaimsFileCheck';
        $testToken = 'test-token';

        // Fake the HTTP request for the external URL.
        \Illuminate\Support\Facades\Http::fake([
            $url =>
                \Illuminate\Http\Client\Factory::response($expected, 200),
        ]);

        $tokenServiceMock = $this->createMock(EClaimsTokenService::class);
        $tokenServiceMock->expects($this->once())
            ->method('getPECWS3Token')
            ->willReturn($testToken);

        // Act - call the service method that makes the HTTP request.
        $send = new ExternalAPIService($tokenServiceMock);
        $result = $send->post($url, $payload);
        // Assert
        $this->assertEquals(json_encode($expected), $result);
    }


    public function tearDown(): void
    {
        parent::tearDown();
    }
}
