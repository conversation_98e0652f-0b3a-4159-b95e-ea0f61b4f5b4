<?php

namespace App\Repositories;

use Illuminate\Support\Facades\DB;

class eSOARepository
{
    /**
     * Create a new class instance.
     */
    public function __construct()
    {
        //
    }

    public function generate(string $enccode): mixed
    {

        $data = DB::select('select * from bghmc_eclaims.dbo.xmlEClaims(?)', [$enccode]);
        $return = [
            "success" => $data[0]->claimXML != null ? true : false,
            "data" => $data
        ];

        return $return;
    }
}
