<?php

namespace App\eClaimsXMLClasses;

use Illuminate\Support\Facades\DB;

class eClaimsXMLClass
{
    /**
     * Create a new class instance.
     */
    public function __construct()
    {
        //
    }

    //generate eClaims XML
    // receives an array of enccodes
    public function generateeClaimsXML(array $enccodes = ['enc1', 'enc2', 'enc3'])
    {
        // $enccodes = ['enc1', 'enc2', 'enc3'];
        $enccodes_string = implode("'), ('", array_map(fn($enc) => addslashes($enc), $enccodes));
        return $enccodes_string;
        return DB::select('
            declare @enccodes enccodesForEClaims;
            insert into @enccodes values (' . $enccodes_string . ');
            exec bghmc_eclaims.dbo.xmlEclaimsv2 @enccodes = @enccodes;
        ');
        // return $enccodes;
    }

    //validate eClaims XML
    //decrypt eClaims XML
    //encrypt eClaims XML
    //send eClaims XML
    //receive eClaims XML
    //parse eClaims XML
    //app\eClaimsXMLClasses\eClaimsXMLClass.php

}
