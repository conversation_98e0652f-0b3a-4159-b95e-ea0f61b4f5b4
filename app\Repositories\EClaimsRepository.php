<?php

namespace App\Repositories;

use App\Interfaces\EClaimsRepositoryInterface;
use Exception;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class EClaimsRepository
{
    /**
     * Create a new class instance.
     */
    public function __construct()
    {
        //
    }

    public function generateeClaimsData(string $enccode): mixed
    {



        $data = DB::select('select * from bghmc_eclaims.dbo.xmlEClaims(?)', [$enccode]);
        $return = [
            "success" => $data[0]->claimXML != null ? true : false,
            "data" => $data
        ];


        return $return;
    }

    public function retrieveeClaimsData(string $enccode): mixed
    {
        $data = DB::table('bghmc_eclaims.dbo.eclaims')
            ->where('enccode', $enccode)
            ->get();

        $return = [
            "success" => $data[0]->claimXML != null ? true : false,
            "data" => $data
        ];

        return $return;
    }

    public function saveeClaimsData(array $claimsData)
    {
        // return $claimsData['data'][0]->claimXML;
        $response = DB::select("exec bghmc_eclaims.dbo.saveClaimsData ?, ?, ?", [
            $claimsData['data'][0]->enccode,
            $claimsData['data'][0]->pHospitalTransmittalNo,
            $claimsData['data'][0]->claimXML
        ]);

        return $response;
    }

    public function saveeCLaimsMapData(array $claimsMapData)
    {

        $response = DB::table('bghmc_eclaims.dbo.eclaims_map')->upsert(
            [
                [
                    'pTransmissionControlNumber' => $claimsMapData['pTransmissionControlNumber'],
                    'pHospitalTransmittalNo' => $claimsMapData['pHospitalTransmittalNo'],
                    'pTransmissionDate' => $claimsMapData['pTransmissionDate'],
                    'pTransmissionTime' => $claimsMapData['pTransmissionTime'],
                    'pReceiptTicketNumber' => $claimsMapData['pReceiptTicketNumber'],
                    'updated_at' => DB::raw('GETDATE()'), // Use GETDATE() for SQL Server
                ]
            ],
            ['pTransmissionControlNumber'], // Unique constraint for upsert
            ['pHospitalTransmittalNo', 'pTransmissionDate', 'pTransmissionTime', 'pReceiptTicketNumber', 'updated_at'] // Columns to update
        );

        if (!$response) {
            return ['success' => false, 'data' => $claimsMapData];
        }

        return ['success' => true, 'data' => $claimsMapData];


    }

    public function getIsClaimEligiblePayload(string $enccode): ?array
    {
        try {
            // Fetch data from the database
            $data = DB::table('bghmc_eclaims.testing.vwCF1')
                ->where('enccode', $enccode)
                ->first();

            // return $data;

            // Handle case where no data is found
            if (!$data) {
                Log::warning("No claim eligibility data found for enccode: {$enccode}");
                return ["success" => false, "message" => "No claim eligibility data found for enccode: {$enccode}"];
            }

            return [
                "success" => true,
                "data" =>
                    [
                        "hospitalCode" => $data->hospitalCode ?? '',
                        "isForOPDHemodialysisClaim" => $data->isForOPDHemodialysisClaim ?? '',
                        "memberPIN" => $data->pMemberPIN ?? '',
                        "memberBasicInformation" => [
                            "lastname" => $data->pMemberLastName ?? '',
                            "firstname" => $data->pMemberFirstName ?? '',
                            "middlename" => $data->pMemberMiddleName ?? '',
                            "maidenname" => '',
                            "suffix" => $data->pMemberSuffix ?? '',
                            'sex' => $data->pMemberSex ?? '',
                            "dateOfBirth" => $data->pMemberBirthDate ?? '',
                        ],
                        "patientIs" => $data->pPatientIs ?? '',
                        "admissionDate" => $data->pAdmissionDate ?? '',
                        "patientPIN" => $data->pPatientPIN ?? '',
                        "patientBasicInformation" => [
                            "lastname" => $data->pPatientLastName ?? '',
                            "firstname" => $data->pPatientFirstName ?? '',
                            "middlename" => $data->pPatientMiddleName ?? '',
                            "maidenname" => '',
                            "suffix" => $data->pPatientSuffix ?? '',
                            "sex" => $data->pPatientSex ?? '',
                            "dateOfBirth" => $data->pPatientBirthDate ?? '',
                        ],
                        "membershipType" => $data->pMemberShipType ?? '',
                        "pEN" => $data->pEN ?? '',
                        "employerName" => $data->employerName ?? '',
                        "isFinal" => $data->isFinal ?? '1', // Defaulting to 1
                    ]
            ];

        } catch (Exception $e) {
            // Log error for debugging
            Log::error("Error fetching claim eligibility data for enccode: {$enccode}. Error: " . $e->getMessage());

            return ["success" => "Error fetching claim eligibility data for enccode: {$enccode}. Error: " . $e->getMessage()]; // Return null or throw a custom exception if needed
        }
    }

    // 



}
