<?php

use App\eClaimsXMLClasses\eClaimsXMLClass;
use App\Http\Controllers\Controller;
use App\Http\Controllers\eClaimsXMLController;
use App\Http\Controllers\EncryptionController;
use App\Http\Controllers\ESOAGenerationController;
use App\Http\Controllers\eSOAXMLController;
use App\Http\Controllers\PECWS3Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Route;

Route::middleware(['auth:sanctum'])->get('/user', function (Request $request) {
    return $request->user();
});

Route::get('/test-server', function () {
    return DB::select('select @@version as version');
});

//api v3
// Route::prefix('v3')->group(function () {
Route::prefix('eclaims3.0')->group(function () {

    //encryption
    Route::group(
        [
            'prefix' => 'encryption',
            'controller' => EncryptionController::class
        ],
        function () {
            Route::post('/encrypt', 'encrypt');
            Route::post('/decrypt', 'decrypt');
            Route::post('/encrypt-ekonsulta', 'encrypt_ekonsulta');
            Route::post('/decrypt-ekonsulta', 'decrypt_ekonsulta');
        }
    );

    //pecws3
    Route::group(
        [
            // 'prefix' => 'pecws3',
            'controller' => PECWS3Controller::class
        ],
        function () {
            Route::get('/sql-version', function () {
                return 'test';
            });
            // Route::get('/getToken', 'getToken');
            Route::get('/getServerVersion', 'getServerVersion');
            Route::get('/getServerDateTime', 'getServerDateTime');
            Route::post('/isDoctorAccredited', 'isDoctorAccredited');
            Route::post('/getMemberPIN', 'getMemberPIN');
            Route::post('/searchCaseRate', 'searchCaseRate');
            Route::post('/searchEmployer', 'searchEmployer');
            Route::post('/getDoctorPAN', 'getDoctorPAN');
            Route::post('/validateeSOA', 'validateeSOA');
            Route::post('/validateCF5', 'validateCF5');
            Route::post('/uploadeClaims', 'uploadeClaims');
            Route::post('/sendeClaimsXMLpecws', 'sendeClaimsXMLpecws');
            Route::post('/getPECWS3Token', 'getPECWS3Token');
            // Route::get('/generateeCLaimsXML', 'generateeCLaimsXML');
        }
    );

    //esoageneration
    Route::group(
        [

            'controller' => ESOAGenerationController::class
        ],
        function () {
            Route::get('/generateESOAStatement', 'generateESOAStatement');
        }
    );

    Route::group(
        [

            'controller' => eClaimsXMLController::class
        ],
        function () {
            Route::post('/generate-eclaims', 'generate');
            Route::post('/retrieve-eclaims', 'retrieve');
            Route::post('/validate-local-eclaims', 'validate_local');
            Route::post('/validate-remote-eclaims', 'validate_remote');

            Route::post('/eClaimsFileCheck', 'eClaimsFileCheck');
            Route::post('/uploadeClaims', 'uploadeClaims');
            Route::post('/isClaimEligible', 'isClaimEligible');
            Route::get('/eClaimsPBEFPDF', 'eClaimsPBEFPDF');
        }
    );

    Route::group(
        [

            'controller' => eSOAXMLController::class
        ],
        function () {
            Route::post('/generate-eSOA', 'generate');
        }
    );

    // Route::get('/debug-xsd', function () {
    //     $xsdPath = public_path('eClaimsDef.xsd');
    //     $xsdPath = str_replace('/', DIRECTORY_SEPARATOR, public_path('eClaimsDef.xsd'));
    //     $xsdPath = app_path('\Services\DTDs\Current\eClaimsDef.xsd');
    //     // return response()->json(['path' => $xsdPath]);

    //     if (file_exists($xsdPath)) {
    //         return response()->json(['message' => 'File exists!', 'path' => $xsdPath]);
    //     } else {
    //         return response()->json(['error' => 'File not found!', 'path' => $xsdPath]);
    //     }
    // });
});
