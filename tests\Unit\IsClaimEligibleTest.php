<?php

namespace Tests\Unit;

use App\Repositories\EClaimsRepository;
use App\Services\PhilhealthEncryptionService;
use Illuminate\Database\QueryException;
use Illuminate\Foundation\Testing\RefreshDatabase;
// use PHPUnit\Framework\TestCase;
use PHPUnit\Framework\Attributes\Test;
use Tests\TestCase;

class IsClaimEligibleTest extends TestCase
{

    protected $claimEligibilityRepositoryMock;
    protected $encrpytionServiceMock;

    private $mockResponse = [
        "hospitalCode" => "20101",
        "isForOPDHemodialysisClaim" => "Y",
        "memberPIN" => "190270605528",
        "memberBasicInformation" => [
            "lastname" => "BAGUIO LN THIRTY",
            "firstname" => "BAGUIO FN THIRTY",
            "middlename" => "BAGUIO MN THIRTY",
            "maidenname" => "",
            "suffix" => ""
        ],
        "patientIs" => "M",
        "admissionDate" => "02-25-2025",
        "patientPIN" => "190270605528",
        "patientBasicInformation" => [
            "lastname" => "BAGUIO LN THIRTY",
            "firstname" => "BAGUIO FN THIRTY",
            "middlename" => "BAGUIO MN THIRTY",
            "maidenname" => "",
            "suffix" => ""
        ],
        "membershipType" => "PS",
        "pEN" => "",
        "employerName" => "",
        "isFinal" => "1"
    ];

    private $mockEnccode = 'OPD1387384Feb252025141035';

    /**
     * A basic unit test example.
     */

    protected function setup(): void
    {
        parent::setUp();

        // Create mocks for each of the dependencies using PHPUnit's createMock
        $this->claimEligibilityRepositoryMock = $this->createMock(EClaimsRepository::class);
        $this->encrpytionServiceMock = $this->createMock(PhilhealthEncryptionService::class);

        // Instantiate the service with the mocked dependencies
    }


    public function test_there_is_generated_payload_with_mock()
    {


        $this->claimEligibilityRepositoryMock
            ->expects($this->once())
            ->method('getIsClaimEligiblePayload')
            ->with($this->mockEnccode)
            ->willReturn($this->mockResponse);

        $actualPayload = $this->claimEligibilityRepositoryMock->getIsClaimEligiblePayload($this->mockEnccode);

        // Assert: Compare arrays instead of JSON strings
        $this->assertEquals($this->mockResponse, $actualPayload);
        $this->assertIsArray($actualPayload, "Payload should be an array.");
    }

    public function test_it_encrypts_and_decrypts_payload_correctly()
    {
        // Arrange: Sample payload
        $payload = $this->mockResponse;

        // Act: Encrypt the payload
        // $encryptionService = new EncryptionService();
        // $encryptedPayload = $this->encrpytionServiceMock->encryptMessage(json_encode($payload));
        $encryptedPayload = $this->encrpytionServiceMock->encryptMessage($payload);

        // Assert: Encrypted payload should not be the same as the original
        // $this->assertIsString($encryptedPayload, "Encrypted payload should be a string.");
        $this->assertNotEquals(json_encode($payload), $encryptedPayload, "Encryption should modify the data.");

        // Act: Decrypt the payload
        $decryptedPayload = $this->encrpytionServiceMock->decryptMessage($encryptedPayload);

        // Assert: Ensure decrypted payload matches the original
        $this->assertEquals($payload, $decryptedPayload, "Decrypted payload should match the original.");
    }



}
