<?php

namespace App\Services;

class BGHMCEclaimsEncryptionService
{



    /**
     * Create a new class instance.
     */

    public function __construct()
    {
        //
    }

    /**
     * Encrypts data according to the guidelines:
     *   1) 256-bit key from passphrase (SHA-256) or random
     *   2) 128-bit IV (random)
     *   3) Null-byte padding
     *   4) AES-256-CBC encryption
     *   5) JSON output with fields: docMimeType, hash, key1, key2, iv, doc
     *
     * @param string      $data       The plaintext to encrypt
     * @param string      $mimeType   The MIME type of the data
     * @param string|null $passphrase An optional passphrase. If null, a random key is used
     * @return string                 JSON-encoded string containing encrypted data and metadata
     */
    public static function encrypt(string $data, string $mimeType, ?string $passphrase = null): string
    {
        // 1) Generate a 256-bit key (32 bytes).
        if ($passphrase) {
            // Derive key from passphrase using SHA-256
            $key = hash('sha256', $passphrase, true); // 32 bytes
        } else {
            // Generate random 32-byte key
            $key = random_bytes(32);
        }

        // 2) Generate a 16-byte (128-bit) IV
        $iv = random_bytes(16);

        // 3) Pad data with null bytes (0x00) so length is multiple of 16
        $blockSize = 16;
        $padLen = $blockSize - (strlen($data) % $blockSize);
        if ($padLen < $blockSize) {
            $data .= str_repeat("\0", $padLen);
        }

        // 4) Encrypt using AES-256-CBC
        //    Note: Using OPENSSL_RAW_DATA so we can manually handle any final encoding.
        $encrypted = openssl_encrypt($data, 'aes-256-cbc', $key, OPENSSL_RAW_DATA, $iv);

        // 5) Build the JSON structure
        $sha256Hash = hash('sha256', $data); // Hash of padded plaintext
        // If a passphrase was used, we typically don't expose the key at all:
        //   key1, key2 => empty
        // Otherwise, we store half in key1, half in key2 (base64-encoded).
        if ($passphrase) {
            $key1 = '';
            $key2 = '';
        } else {
            $key1 = base64_encode(substr($key, 0, 16));
            $key2 = base64_encode(substr($key, 16, 16));
        }

        $result = [
            'docMimeType' => $mimeType,
            'hash'        => $sha256Hash,
            'key1'        => $key1,
            'key2'        => $key2,
            'iv'          => base64_encode($iv),
            'doc'         => base64_encode($encrypted)
        ];

        return json_encode($result);
    }
}
