<?php

namespace App\Http\Controllers;

use App\Http\Controllers\Controller;
use App\Services\EClaimsTokenService;
use App\Services\PECWS3Service;
use Illuminate\Http\Request;

class PECWS3Controller extends Controller
{
    protected $pecws3Service;
    protected $tokenService;

    public function __construct(
        PECWS3Service $pecws3Service,
        EClaimsTokenService $tokenService
    ) {
        $this->pecws3Service = $pecws3Service;
        $this->tokenService = $tokenService;
    }

    public function getPECWS3Token(Request $request)
    {
        return $this->tokenService->getPECWS3Token();
    }

    public function getServerVersion()
    {
        return $this->pecws3Service->getPECWS3ServerVersion();
    }

    public function getServerDateTime()
    {
        return $this->pecws3Service->getPECWS3ServerDateTime();
    }

    public function isDoctorAccredited(Request $request)
    {
        return $this->pecws3Service->isDoctorAccredited($request);
    }

    public function getMemberPIN(Request $request)
    {
        // return $request;   
        return $this->pecws3Service->getMemberPIN($request);
    }

    public function searchCaseRate(Request $request)
    {
        return $this->pecws3Service->searchCaseRate($request);
    }

    public function searchEmployer(Request $request)
    {
        return $this->pecws3Service->searchEmployer($request);
    }

    public function getDoctorPAN(Request $request)
    {
        return $this->pecws3Service->getDoctorPAN($request);
    }
    public function validateeSOA(Request $request)
    {
        return $this->pecws3Service->validateeSOA($request);
    }

    public function validateCF5(Request $request)
    {
        return $this->pecws3Service->validateCF5($request);
    }
    public function uploadeClaims(Request $request)
    {
        return $this->pecws3Service->uploadeClaims($request);
    }

    public function sendeClaimsXMLpecws(Request $request)
    {

        return $this->pecws3Service->eClaimsFileCheck($request);
    }
}
