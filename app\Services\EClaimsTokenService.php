<?php

namespace App\Services;

use Illuminate\Support\Facades\Http;

class EClaimsTokenService
{
    protected $baseUrl;
    protected $accreditationNo;
    protected $softwareCertificateId;
    /**
     * Create a new class instance.
     */
    public function __construct()
    {
        //
        $this->accreditationNo = config("pecws3.eclaims_dev_accreditation_no");
        $this->softwareCertificateId = config("pecws3.eclaims_dev_software_certificate_id");
        $this->baseUrl = config("pecws3.eclaims_dev_url");

    }

    public function getPECWS3Token(): string|array
    {
        $headers = [
            'accreditationNo' => $this->accreditationNo,
            'softwareCertificateId' => $this->softwareCertificateId,
            'Content-Type' => 'application/json',
            // 'accreditationNo' => 'H13000204',
            // 'softwareCertificateId' => 'ECLAIMS-3.0-20101-DUMMY',
            // 'Content-Type' => 'application/json',
        ];
        // return $headers;
        // return json_decode(json_encode($headers));
        // $response = Http::withHeaders($headers)->get('http://ecstest.philhealth.gov.ph/PHIC/Claims3.0/getToken');
        // var_dump($headers);
        $response = Http::withHeaders($headers)->get($this->baseUrl . '/getToken');

        return json_decode($response)->result;
    }
}
