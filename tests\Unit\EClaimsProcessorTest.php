<?php

namespace Tests\Unit;

use App\Interfaces\Contracts\XMLProcessorInterface;
use App\Repositories\EClaimsRepository;
use App\Services\Validators\eClaimsValidatorService;
use App\Services\XMLServices\EClaimsProcessor;
// use App\Services\XMLServices\EClaimsXMLService;
use Tests\TestCase;
use PHPUnit\Framework\Attributes\Test;



class EClaimsProcessorTest extends TestCase
{
    protected $eClaimsRepositoryMock;
    // protected $eClaimsXMLServiceMock;

    protected $eClaimsValidatorMock;
    protected $processor;

    protected function setUp(): void
    {
        parent::setUp();

        // Create mocks for dependencies
        $this->eClaimsRepositoryMock = $this->createMock(EClaimsRepository::class);
        // $this->eClaimsXMLServiceMock = $this->createMock(EClaimsXMLService::class);
        $this->eClaimsValidatorMock = $this->createMock(eClaimsValidatorService::class);

        // Instantiate the processor with mocked dependencies
        $this->processor = new EClaimsProcessor(
            $this->eClaimsRepositoryMock,
            $this->eClaimsValidatorMock


            // $this->eClaimsXMLServiceMock
        );
    }

    #[Test]
    public function test_implements_xml_processor_interface(): void
    {
        $this->assertInstanceOf(XMLProcessorInterface::class, $this->processor);
    }

    #[Test]
    public function test_retrieves_xml_from_database(): void
    {
        // Arrange
        $enccode = 1;
        $expectedXml = '<eClaims>Test XML</eClaims>';

        // Mock repository response
        $this->eClaimsRepositoryMock
            ->expects($this->once())
            ->method('getEClaimsData')
            ->with($enccode)
            ->willReturn($expectedXml);

        // Act
        $result = $this->processor->retrieve($enccode);

        // Assert
        $this->assertEquals($expectedXml, $result);
        $this->assertStringContainsString('<eClaims>', $result);
    }

    #[Test]
    public function test_validates_xml_locally(): void
    {
        // Arrange
        $validXml = '<eClaims>Valid</eClaims>';
        $invalidXml = '<Invalid>Data</Invalid>';

        // Act & Assert
        $this->assertTrue($this->processor->validate_local($validXml));
        $this->assertFalse($this->processor->validate_local($invalidXml));
    }

    #[Test]
    public function test_encrypts_payload(): void
    {
        // Arrange
        $xml = '<eClaims>Data</eClaims>';
        $expectedEncrypted = base64_encode($xml);

        // Act
        $result = $this->processor->encrypt($xml);

        // Assert
        $this->assertNotEquals($xml, $result);
        $this->assertEquals($expectedEncrypted, $result);
    }

    #[Test]
    public function test_validates_payload_remotely(): void
    {
        // Arrange
        $encryptedPayload = base64_encode('<eClaims>Data</eClaims>');

        // Mock XMLService's remote validation
        $this->processor->encrypt($encryptedPayload);

        // Act
        $result = $this->processor->validate_remote($encryptedPayload);

        // Assert
        $this->assertTrue($result);
    }

    #[Test]
    public function test_posts_to_api(): void
    {
        // Arrange
        $encryptedPayload = base64_encode('<eClaims>Data</eClaims>');
        $expectedResponse = ['status' => 'success'];

        // Mock API post request
        $this->processor->post($encryptedPayload);

        // Act
        $result = $this->processor->post($encryptedPayload);

        // Assert
        $this->assertIsArray($result);
        $this->assertEquals($expectedResponse, $result);
    }

    public function tearDown(): void
    {
        parent::tearDown();
    }
}
