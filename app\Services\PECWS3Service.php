<?php

namespace App\Services;

use App\Traits\HasPECWSToken;
// use Illuminate\Container\Attributes\Log;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;

class PECWS3Service
{
    use HasPECWSToken;

    protected $philhealthEncryptionService;
    protected $baseUrl;
    /**
     * Create a new class instance.
     */
    public function __construct(PhilhealthEncryptionService $philhealthEncryptionService)
    {
        $this->philhealthEncryptionService = $philhealthEncryptionService;
        $this->baseUrl = config('pecws3.eclaims_dev_url');
    }

    private function getToken()
    {
        return $this->getPECWS3Token();
    }

    public function getPECWS3ServerVersion()
    {

        $headers = [
            'token' => $this->getToken(),
            'Content-Type' => 'application/json',
        ];

        $response = Http::withHeaders($headers)
            ->get('http://ecstest.philhealth.gov.ph/PHIC/Claims3.0/getServerVersion');

        return json_decode($response);
    }

    public function getPECWS3ServerDateTime()
    {
        $headers = [
            'token' => $this->getToken(),
            'Content-Type' => 'application/json',
        ];

        $response = Http::withHeaders($headers)
            ->get('http://ecstest.philhealth.gov.ph/PHIC/Claims3.0/getDBServerDateTime');

        return json_decode($response);
    }

    // public function isDoctorAccredited(Request $request)
    public function isDoctorAccredited($data)
    {
        $headers = [
            'token' => $this->getToken(),
            'Content-Type' => 'application/json',
        ];

        // return $data;
        // $data = [
        //     'accrecode' => '**********',
        //     'admissionDate' => '01-01-1990',
        //     'dischargeDate' => '01-01-1990',
        // ];

        $encryptedResponse = Http::withHeaders($headers)
            ->post('http://ecstest.philhealth.gov.ph/PHIC/Claims3.0/isDoctorAccredited', $data);

        $decryptedResponse = $this->philhealthEncryptionService->decryptMessage($encryptedResponse['result']);

        return $decryptedResponse . "}";
    }

    public function getMemberPIN($data)
    {
        $headers = [
            'token' => $this->getToken(),
            'Content-Type' => 'application/json'
        ];

        $encryptedMessage = $this->philhealthEncryptionService->encryptMessage($data->getContent());

        $encryptedResponse = Http::withHeaders($headers)
            ->post('http://ecstest.philhealth.gov.ph/PHIC/Claims3.0/getMemberPIN', $encryptedMessage);

        if ($encryptedResponse['success'] == false) {
            return $encryptedResponse;
        }

        $decryptedResponse = $this->philhealthEncryptionService->decryptMessage($encryptedResponse['result']);

        return $decryptedResponse . "}";
    }

    public function searchCaseRate($data)
    {
        $headers = [
            'token' => $this->getToken(),
            'Content-Type' => 'application/json',
            'Accept' => 'application/json'
        ];

        //encryption not required so should be passed as array
        //convert null values to ""

        $data = [
            'icdcode' => $data->input('icdcode') ?? '', // Use null coalescing to ensure default is an empty string
            'rvscode' => $data->input('rvscode') ?? '', // Ensure default is an empty string
            'description' => $data->input('description', 'DENGUE'), // Default to 'DENGUE' if not provided
            'targetdate' => $data->input('targetdate', '02-14-2024') // Default to '02-14-2024' if not provided
        ];


        $encryptedResponse = Http::withHeaders($headers)
            ->post('http://ecstest.philhealth.gov.ph/PHIC/Claims3.0/searchCaseRates', $data);


        $decryptedResponse = $this->philhealthEncryptionService->decryptMessage($encryptedResponse['result']);

        return $decryptedResponse . "}";
    }

    public function getDoctorPAN($data)
    {
        $headers = [
            'token' => $this->getToken(),
            'Content-Type' => 'application/json',
        ];

        // $data = [
        //     // 'tin' => $data->input('tin', '100-123-456'),
        //     'lastname' => $data->input('lastname', 'ON'),
        //     'firstname' => $data->input('firstname', 'LIFE'),
        //     'middlename' => $data->input('middlename', 'GOES'),
        //     'suffix' => $data->input('suffix') ?? '',
        //     'birthdate' => $data->input('birthdate', '10-11-1992'),
        // ];

        // return $data->getContent();

        // $encryptedMessage = $this->philhealthEncryptionService->encryptMessage(json_encode($data));
        $encryptedMessage = $this->philhealthEncryptionService->encryptMessage($data->getContent());

        // return $encryptedMessage;

        $encryptedResponse = Http::withHeaders($headers)
            ->post('http://ecstest.philhealth.gov.ph/PHIC/Claims3.0/getDoctorPAN', $encryptedMessage);

        if ($encryptedResponse['success'] == false) {
            return $encryptedResponse;
        }

        $decryptedResponse = $this->philhealthEncryptionService->decryptMessage($encryptedResponse['result']);

        return $decryptedResponse . "}";
    }

    public function searchEmployer($data)
    {
        $headers = [
            'token' => $this->getToken(),
            'Content-Type' => 'application/json',
            // 'Accept' => 'application/json'
        ];

        $data = [
            'philhealthno' => $data->input('philhealthno', 'PHILIPPINE HEALTH INSURANCE CORPORATION'),
            'employername' => $data->input('employername', '000000000000000'),
        ];

        $encryptedResponse = Http::withHeaders($headers)
            ->post('http://ecstest.philhealth.gov.ph/PHIC/Claims3.0/searchEmployer', $data);

        if ($encryptedResponse['success'] == false) {
            return $encryptedResponse;
        }


        $decryptedResponse = $this->philhealthEncryptionService->decryptMessage($encryptedResponse['result']);


        return $decryptedResponse . "}";
        // return json_decode($decryptedResponse);
    }
    public function validateeSOA($data)
    {
        $headers = [
            'token' => $this->getToken(),
            'Content-Type' => 'application/json',
            // 'Accept' => 'application/json'
        ];


        $encryptedMessage = $this->philhealthEncryptionService->encryptMessage($data->getContent() . ">");


        $encryptedResponse = Http::withHeaders($headers)
            ->post('http://ecstest.philhealth.gov.ph/PHIC/Claims3.0/validateeSOA', $encryptedMessage);

        if ($encryptedResponse === 'Request failed.' || !is_array($encryptedResponse) || empty($encryptedResponse['success'])) {
            return $encryptedResponse;
        }

        if ($encryptedResponse['success'] == false) {
            return $encryptedResponse;
        }

        $decryptedResponse = $this->philhealthEncryptionService->decryptMessage($encryptedResponse['result']);


        return $decryptedResponse . "}";
    }

    public function validateCF5($data)
    {
        $headers = [
            'token' => $this->getToken(),
            'Content-Type' => 'application/json',
            // 'Accept' => 'application/json'
        ];



        // return $data->all();

        //         $data = $data->getContent() ?? [
        //             "xml" => '<CF5  
        //     pHospitalCode="300806"> 
        //   <DRGCLAIM  
        //     ClaimNumber="300806-20221216-1-1" 
        //     PrimaryCode="A00.0" 
        //     NewBornTimeOfBirth=""     NewBornAdmWeight="2"     Remarks=""> 
        //     <SECONDARYDIAGS> 
        //       <SECONDARYDIAG  
        //         SecondaryCode="A00.1" 
        //         Remarks=""/> 
        //     </SECONDARYDIAGS> 
        //     <PROCEDURES> 
        //       <PROCEDURE  
        //         RvsCode="" 
        //         Laterality="" 
        //         Ext1="" 
        //         Ext2="" 
        //         Remarks=""/> 
        //     </PROCEDURES> 
        //   </DRGCLAIM> 
        // </CF5>
        // '
        //         ];
        // return $data->getContent();
        // $encryptedMessage = $this->philhealthEncryptionService->encryptMessage($data['xml']);
        // $encryptedMessage = $this->philhealthEncryptionService->encryptMessage($data->getContent() . ">");
        $encryptedMessage = $data;
        // return $encryptedMessage;

        $encryptedResponse = Http::withHeaders($headers)
            ->post('http://ecstest.philhealth.gov.ph/PHIC/Claims3.0/validateCF5', $encryptedMessage);

        // return $encryptedResponse;


        if ($encryptedResponse['success'] == false && isset($encryptedResponse['result'])) {
            return $this->philhealthEncryptionService->decryptMessage($encryptedResponse['result']) . "]"[0];
        }

        if ($encryptedResponse === 'Request failed.' || !is_array($encryptedResponse) || empty($encryptedResponse['success'])) {
            return $encryptedResponse;
        }

        if ($encryptedResponse['success'] == false) {
            return $encryptedResponse;
        }

        $decryptedResponse = $this->philhealthEncryptionService->decryptMessage($encryptedResponse['result']);


        return $decryptedResponse . "}";
    }

    public function uploadeClaims($data)
    {
        // return $this->baseUrl . '/eClaimsFileCheck';
        $headers = [
            'token' => $this->getToken(),
            'Content-Type' => 'application/json',
            // 'Accept' => 'application/json'
        ];

        // return $data->getContent();



        // $encryptedMessage = $data->getContent();


        $encryptedMessage = $this->philhealthEncryptionService->encryptMessage($data->getContent());

        // return $encryptedMessage;

        $encryptedResponse = Http::withHeaders($headers)
            ->post('http://ecstest.philhealth.gov.ph/PHIC/Claims3.0/uploadeClaims', $encryptedMessage);

        // if ($encryptedResponse == 'Request failed.') {
        //     return 'tyeasdfa';
        // }

        // return $encryptedResponse;



        // if ($encryptedResponse === 'Request failed.' || !is_array($encryptedResponse) || empty($encryptedResponse['success'])) {
        //     return $encryptedResponse;
        // }

        // if ($encryptedResponse['success'] == false) {
        //     return $encryptedResponse;
        // }


        $decryptedResponse = $this->philhealthEncryptionService->decryptMessage($encryptedResponse['result']);
        return $decryptedResponse . '>';
    }


    // public function eClaimsFileCheck($data)
    // {
    //     /* 
    //         receive generated eClaims XML -> $data
    //         validate eClaims
    //             validate with ectest.philhealth.gov.ph
    //                 encrypt eClaims XML
    //                 send eClaims XML to ectest.philhealth.gov.ph
    //                 receive eClaims XML response
    //                     if offline
    //                         validate with local DTD
    //                 decrypt eClaims XML response
    //     */
    //     $headers = [
    //         'token' => $this->getToken(),
    //         'Content-Type' => 'application/json',
    //         // 'Accept' => 'application/json'
    //     ];

    //     // return $data;
    //     // return $headers;

    //     $encryptedMessage = $this->philhealthEncryptionService->encryptMessage($data->getContent());
    //     // return $encryptedMessage;

    //     $client = new ExternalApiService();
    //     $response = $client->post('eClaimsFileCheck', $encryptedMessage);

    //     // return $response;

    //     $encryptedResponse = Http::withHeaders($headers)
    //         ->post('http://ecstest.philhealth.gov.ph/PHIC/Claims3.0/eClaimsFileCheck', $encryptedMessage);

    //     return $encryptedResponse;
    // }
}
